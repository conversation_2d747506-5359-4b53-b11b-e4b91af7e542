import { User } from '@/types';
import { supabase } from '@/lib/supabase';
import React, { createContext, useContext, useEffect, useState } from 'react';

interface MagicLinkSession {
  id: string;
  accessRightId: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  currentMagicLink: MagicLinkSession | null;
  isMagicLinkSession: boolean;
}

const SupabaseAuthContext = createContext<AuthContextType | undefined>(undefined);

export const SupabaseAuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentMagicLink, setCurrentMagicLink] = useState<MagicLinkSession | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      setIsLoading(true);
      
      // Check for existing session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session) {
        // Get user data from Supabase
        const { data: userData, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', session.user.id)
          .single();
        
        if (error) {
          console.error('Error fetching user data:', error);
        } else if (userData) {
          setUser({
            id: userData.id,
            username: userData.username,
            role: userData.role,
            email: userData.email,
            fullName: userData.full_name
          });
        }
      } else {
        // Check for magic link session in localStorage
        const savedMagicLink = localStorage.getItem('currentMagicLink');
        if (savedMagicLink) {
          try {
            setCurrentMagicLink(JSON.parse(savedMagicLink));
          } catch (e) {
            console.error('Error parsing saved magic link:', e);
            localStorage.removeItem('currentMagicLink');
          }
        }
      }
      
      setIsLoading(false);
    };

    checkAuth();
    
    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session) {
          // Get user data from Supabase
          const { data: userData, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', session.user.id)
            .single();
          
          if (error) {
            console.error('Error fetching user data:', error);
          } else if (userData) {
            setUser({
              id: userData.id,
              username: userData.username,
              role: userData.role,
              email: userData.email,
              fullName: userData.full_name
            });
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const login = async (username: string, password: string) => {
    setIsLoading(true);
    
    try {
      // Find user by username
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('username', username)
        .single();
      
      if (userError) {
        console.error('Error finding user:', userError);
        throw new Error('Invalid username or password');
      }
      
      // For demo purposes, we're not actually checking the password
      // In a real app, you would use Supabase Auth with email/password
      // or another authentication method
      
      // Sign in with Supabase Auth (in a real app)
      // const { error } = await supabase.auth.signInWithPassword({
      //   email: userData.email,
      //   password: password
      // });
      
      // if (error) {
      //   console.error('Error signing in:', error);
      //   throw error;
      // }
      
      // For demo, just set the user directly
      setUser({
        id: userData.id,
        username: userData.username,
        role: userData.role,
        email: userData.email,
        fullName: userData.full_name
      });
      
      // If user is logging in normally, remove any magic link session
      localStorage.removeItem('currentMagicLink');
      setCurrentMagicLink(null);
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    // Sign out from Supabase Auth
    await supabase.auth.signOut();
    
    // Clear local state
    localStorage.removeItem('currentMagicLink');
    setUser(null);
    setCurrentMagicLink(null);
  };

  return (
    <SupabaseAuthContext.Provider value={{
      user,
      isLoading,
      login,
      logout,
      isAuthenticated: !!user || !!currentMagicLink,
      currentMagicLink,
      isMagicLinkSession: !user && !!currentMagicLink
    }}>
      {children}
    </SupabaseAuthContext.Provider>
  );
};

export const useSupabaseAuth = () => {
  const context = useContext(SupabaseAuthContext);
  if (context === undefined) {
    throw new Error('useSupabaseAuth must be used within a SupabaseAuthProvider');
  }
  return context;
};
