import { supabase } from '@/lib/supabase';
import { RegistrationLink, ProgramShareLink } from '@/types';
import { convertKeysToCamel, convertKeysToSnake } from './supabase.service.helpers';
import { v4 as uuidv4 } from 'uuid';

// Registration Links Service
export const registrationLinksService = {
  async getAll(): Promise<RegistrationLink[]> {
    const { data, error } = await supabase
      .from('registration_links')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching registration links:', error);
      throw error;
    }
    
    return data.map(link => {
      const converted = convertKeysToCamel(link);
      return {
        ...converted,
        expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null
      } as RegistrationLink;
    });
  },
  
  async getById(id: string): Promise<RegistrationLink | null> {
    const { data, error } = await supabase
      .from('registration_links')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error(`Error fetching registration link with id ${id}:`, error);
      throw error;
    }
    
    if (!data) return null;
    
    const converted = convertKeysToCamel(data);
    
    return {
      ...converted,
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null
    } as RegistrationLink;
  },
  
  async getByUrl(url: string): Promise<RegistrationLink | null> {
    const { data, error } = await supabase
      .from('registration_links')
      .select('*')
      .eq('url', url)
      .single();
    
    if (error) {
      console.error(`Error fetching registration link with url ${url}:`, error);
      throw error;
    }
    
    if (!data) return null;
    
    const converted = convertKeysToCamel(data);
    
    return {
      ...converted,
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null
    } as RegistrationLink;
  },
  
  async create(link: Omit<RegistrationLink, 'id'>): Promise<RegistrationLink> {
    // Generate a unique URL if not provided
    const url = link.url || `/register/${uuidv4()}`;
    
    const linkToInsert = {
      ...convertKeysToSnake(link),
      url,
      expires_at: link.expiresAt ? link.expiresAt.toISOString() : null
    };
    
    const { data, error } = await supabase
      .from('registration_links')
      .insert(linkToInsert)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating registration link:', error);
      throw error;
    }
    
    const converted = convertKeysToCamel(data);
    
    return {
      ...converted,
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null
    } as RegistrationLink;
  },
  
  async update(id: string, updates: Partial<Omit<RegistrationLink, 'id'>>): Promise<RegistrationLink> {
    const linkToUpdate: any = convertKeysToSnake(updates);
    
    if (updates.expiresAt) {
      linkToUpdate.expires_at = updates.expiresAt.toISOString();
    }
    
    const { data, error } = await supabase
      .from('registration_links')
      .update(linkToUpdate)
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error(`Error updating registration link with id ${id}:`, error);
      throw error;
    }
    
    const converted = convertKeysToCamel(data);
    
    return {
      ...converted,
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null
    } as RegistrationLink;
  },
  
  async incrementRegistrationCount(id: string): Promise<RegistrationLink> {
    // First get the current count
    const { data: currentData, error: fetchError } = await supabase
      .from('registration_links')
      .select('current_registrations')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      console.error(`Error fetching registration link with id ${id}:`, fetchError);
      throw fetchError;
    }
    
    const currentCount = currentData.current_registrations;
    
    // Then update the count
    const { data, error } = await supabase
      .from('registration_links')
      .update({ current_registrations: currentCount + 1 })
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error(`Error incrementing registration count for link with id ${id}:`, error);
      throw error;
    }
    
    const converted = convertKeysToCamel(data);
    
    return {
      ...converted,
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null
    } as RegistrationLink;
  },
  
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('registration_links')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error(`Error deleting registration link with id ${id}:`, error);
      throw error;
    }
  }
};

// Program Share Links Service
export const programShareLinksService = {
  async getAll(): Promise<ProgramShareLink[]> {
    const { data, error } = await supabase
      .from('program_share_links')
      .select('*')
      .order('created_at', { ascending: false });
    
    if (error) {
      console.error('Error fetching program share links:', error);
      throw error;
    }
    
    return data.map(link => convertKeysToCamel(link)) as ProgramShareLink[];
  },
  
  async getById(id: string): Promise<ProgramShareLink | null> {
    const { data, error } = await supabase
      .from('program_share_links')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      console.error(`Error fetching program share link with id ${id}:`, error);
      throw error;
    }
    
    if (!data) return null;
    
    return convertKeysToCamel(data) as ProgramShareLink;
  },
  
  async getByProgramId(programId: string): Promise<ProgramShareLink | null> {
    const { data, error } = await supabase
      .from('program_share_links')
      .select('*')
      .eq('program_id', programId)
      .single();
    
    if (error) {
      console.error(`Error fetching program share link for program ${programId}:`, error);
      throw error;
    }
    
    if (!data) return null;
    
    return convertKeysToCamel(data) as ProgramShareLink;
  },
  
  async create(link: Omit<ProgramShareLink, 'id'>): Promise<ProgramShareLink> {
    // Generate a unique URL if not provided
    const url = link.url || `/program/share/${uuidv4()}`;
    
    const linkToInsert = {
      ...convertKeysToSnake(link),
      url,
      view_count: link.viewCount || 0
    };
    
    const { data, error } = await supabase
      .from('program_share_links')
      .insert(linkToInsert)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating program share link:', error);
      throw error;
    }
    
    return convertKeysToCamel(data) as ProgramShareLink;
  },
  
  async incrementViewCount(id: string): Promise<ProgramShareLink> {
    // First get the current count
    const { data: currentData, error: fetchError } = await supabase
      .from('program_share_links')
      .select('view_count')
      .eq('id', id)
      .single();
    
    if (fetchError) {
      console.error(`Error fetching program share link with id ${id}:`, fetchError);
      throw fetchError;
    }
    
    const currentCount = currentData.view_count;
    
    // Then update the count
    const { data, error } = await supabase
      .from('program_share_links')
      .update({ view_count: currentCount + 1 })
      .eq('id', id)
      .select()
      .single();
    
    if (error) {
      console.error(`Error incrementing view count for program share link with id ${id}:`, error);
      throw error;
    }
    
    return convertKeysToCamel(data) as ProgramShareLink;
  },
  
  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('program_share_links')
      .delete()
      .eq('id', id);
    
    if (error) {
      console.error(`Error deleting program share link with id ${id}:`, error);
      throw error;
    }
  }
};
