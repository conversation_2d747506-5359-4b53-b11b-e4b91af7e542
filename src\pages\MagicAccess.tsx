import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { useData } from "@/contexts/DataContext";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { CalendarClock, Check, Key, ShieldAlert, ShieldCheck } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";

const MagicAccess = () => {
  const { linkId } = useParams<{ linkId: string }>();
  const navigate = useNavigate();
  const {
    magicLinks,
    getMagicLinkById,
    getAccessRightById,
    addAccessLog
  } = useData();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [magicLink, setMagicLink] = useState<any>(null);
  const [accessRight, setAccessRight] = useState<any>(null);

  useEffect(() => {
    if (!linkId) {
      setError("Lien d'accès invalide");
      setIsLoading(false);
      return;
    }

    // Récupérer le lien magique
    const link = getMagicLinkById(linkId);
    if (!link) {
      setError("Lien d'accès introuvable");
      setIsLoading(false);
      return;
    }

    // Vérifier si le lien est actif
    if (!link.active) {
      setError("Ce lien d'accès a été révoqué");
      setIsLoading(false);
      return;
    }

    // Vérifier si le lien n'a pas expiré
    if (link.expiresAt && new Date() > new Date(link.expiresAt)) {
      setError("Ce lien d'accès a expiré");
      setIsLoading(false);
      return;
    }

    // Récupérer les droits d'accès associés
    const right = getAccessRightById(link.accessRightId);
    if (!right) {
      setError("Droits d'accès introuvables");
      setIsLoading(false);
      return;
    }

    setMagicLink(link);
    setAccessRight(right);

    // Enregistrer l'accès
    addAccessLog({
      magicLinkId: link.id,
      action: 'view',
      feature: 'youths', // Fonctionnalité par défaut pour l'accès initial
      details: 'Accès initial via lien magique'
    });

    // Stocker le lien magique dans le localStorage pour l'utiliser dans l'application
    localStorage.setItem('currentMagicLink', JSON.stringify({
      id: link.id,
      accessRightId: link.accessRightId
    }));

    setIsLoading(false);
  }, [linkId, getMagicLinkById, getAccessRightById, addAccessLog]);

  const handleContinue = () => {
    toast({
      title: "Accès accordé",
      description: "Vous accédez maintenant à l'application avec les droits définis"
    });
    navigate('/');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification du lien d'accès...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <Card className="w-full max-w-md">
          <CardHeader>
            <div className="flex justify-center mb-4">
              <div className="p-3 rounded-full bg-red-100">
                <ShieldAlert className="h-8 w-8 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-center text-xl">Accès refusé</CardTitle>
            <CardDescription className="text-center">{error}</CardDescription>
          </CardHeader>
          <CardFooter className="flex justify-center">
            <Button variant="outline" onClick={() => navigate('/')}>
              Retour à l'accueil
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex justify-center mb-4">
            <div className="p-3 rounded-full bg-green-100">
              <ShieldCheck className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <CardTitle className="text-center text-xl">Accès autorisé</CardTitle>
          <CardDescription className="text-center">
            Vous accédez à l'application via un lien magique
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center">
              <Key className="h-4 w-4 mr-2 text-gray-500" />
              <span className="text-sm font-medium">Nom du lien :</span>
            </div>
            <p className="text-sm pl-6">{magicLink.name}</p>
          </div>
          <div className="space-y-2">
            <div className="flex items-center">
              <Check className="h-4 w-4 mr-2 text-gray-500" />
              <span className="text-sm font-medium">Droits d'accès :</span>
            </div>
            <p className="text-sm pl-6">{accessRight.name}</p>
            <p className="text-sm pl-6 text-gray-500">{accessRight.description}</p>
          </div>
          {magicLink.expiresAt && (
            <div className="space-y-2">
              <div className="flex items-center">
                <CalendarClock className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm font-medium">Date d'expiration :</span>
              </div>
              <p className="text-sm pl-6">
                {format(new Date(magicLink.expiresAt), 'dd MMMM yyyy', { locale: fr })}
              </p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button onClick={handleContinue}>
            Continuer vers l'application
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default MagicAccess;
