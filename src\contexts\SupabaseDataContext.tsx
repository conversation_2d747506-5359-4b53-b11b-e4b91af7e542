import * as services from '@/services';
import { AccessLog, AccessRight, AccessType, Assembly, DailyProgram, District, FeatureType, MagicLink, Payment, Permission, ProgramActivity, ProgramShareLink, Region, RegistrationLink, Sermon, Youth } from '@/types';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import React, { createContext, useContext } from 'react';
import { v4 as uuidv4 } from 'uuid';

interface DataContextType {
  // Data
  regions: Region[];
  districts: District[];
  assemblies: Assembly[];
  youths: Youth[];
  payments: Payment[];
  permissions: Permission[];
  sermons: Sermon[];
  dailyPrograms: DailyProgram[];
  registrationLinks: RegistrationLink[];
  programShareLinks: ProgramShareLink[];
  accessRights: AccessRight[];
  magicLinks: MagicLink[];
  accessLogs: AccessLog[];

  // Loading states
  isLoading: {
    regions: boolean;
    districts: boolean;
    assemblies: boolean;
    youths: boolean;
    payments: boolean;
    permissions: boolean;
    sermons: boolean;
    dailyPrograms: boolean;
    registrationLinks: boolean;
    programShareLinks: boolean;
    accessRights: boolean;
    magicLinks: boolean;
    accessLogs: boolean;
  };

  // Error states
  errors: {
    regions: Error | null;
    districts: Error | null;
    assemblies: Error | null;
    youths: Error | null;
    payments: Error | null;
    permissions: Error | null;
    sermons: Error | null;
    dailyPrograms: Error | null;
    registrationLinks: Error | null;
    programShareLinks: Error | null;
    accessRights: Error | null;
    magicLinks: Error | null;
    accessLogs: Error | null;
  };

  // CRUD operations
  addRegion: (name: string) => Promise<Region>;
  updateRegion: (id: string, updates: Partial<Omit<Region, 'id'>>) => Promise<Region>;
  deleteRegion: (id: string) => Promise<void>;

  addDistrict: (name: string, regionId: string) => Promise<District>;
  updateDistrict: (id: string, updates: Partial<Omit<District, 'id'>>) => Promise<District>;
  deleteDistrict: (id: string) => Promise<void>;

  addAssembly: (name: string, districtId: string) => Promise<Assembly>;
  updateAssembly: (id: string, updates: Partial<Omit<Assembly, 'id'>>) => Promise<Assembly>;
  deleteAssembly: (id: string) => Promise<void>;

  addYouth: (youth: Omit<Youth, 'id' | 'payments' | 'registrationDate'>) => Promise<Youth>;
  updateYouth: (id: string, updates: Partial<Youth>) => Promise<Youth>;
  deleteYouth: (id: string) => Promise<void>;

  addPayment: (payment: Omit<Payment, 'id'>) => Promise<Payment>;
  updatePayment: (id: string, updates: Partial<Omit<Payment, 'id'>>) => Promise<Payment>;
  deletePayment: (id: string) => Promise<void>;

  addPermission: (permission: Omit<Permission, 'id'>) => Promise<Permission>;
  approvePermission: (id: string, approverId: string) => Promise<Permission>;
  deletePermission: (id: string) => Promise<void>;

  addSermon: (sermon: Omit<Sermon, 'id'>) => Promise<Sermon>;
  updateSermon: (id: string, updates: Partial<Omit<Sermon, 'id'>>) => Promise<Sermon>;
  deleteSermon: (id: string) => Promise<void>;

  addDailyProgram: (program: Omit<DailyProgram, 'id'>) => Promise<DailyProgram>;
  updateDailyProgram: (id: string, updates: Partial<Omit<DailyProgram, 'id'>>) => Promise<DailyProgram>;
  updateProgramActivities: (programId: string, activities: ProgramActivity[]) => Promise<void>;
  deleteDailyProgram: (id: string) => Promise<void>;

  createRegistrationLink: () => Promise<RegistrationLink>;
  deleteRegistrationLink: (id: string) => Promise<void>;
  incrementRegistrationCount: (linkIdentifier: string) => Promise<void>;

  createProgramShareLink: (programId: string) => Promise<ProgramShareLink>;
  incrementProgramShareViewCount: (linkId: string) => Promise<void>;

  // Access rights functions
  createAccessRight: (accessRight: Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>) => Promise<AccessRight>;
  updateAccessRight: (id: string, updates: Partial<Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<AccessRight>;
  deleteAccessRight: (id: string) => Promise<void>;
  getAccessRightById: (id: string) => AccessRight | undefined;

  // Magic links functions
  createMagicLink: (accessRightId: string, name: string, expiresAt?: Date) => Promise<MagicLink>;
  updateMagicLink: (id: string, updates: Partial<Omit<MagicLink, 'id' | 'createdAt'>>) => Promise<MagicLink>;
  revokeMagicLink: (id: string) => Promise<void>;
  deleteMagicLink: (id: string) => Promise<void>;
  getMagicLinkById: (id: string) => MagicLink | undefined;
  getMagicLinksByAccessRightId: (accessRightId: string) => MagicLink[];
  incrementMagicLinkViewCount: (id: string) => Promise<void>;
  incrementMagicLinkEditCount: (id: string) => Promise<void>;

  // Access logs functions
  addAccessLog: (log: Omit<AccessLog, 'id' | 'timestamp'>) => Promise<void>;
  getAccessLogsByMagicLinkId: (magicLinkId: string) => AccessLog[];

  // Access check function
  checkAccess: (magicLinkId: string, feature: FeatureType, accessType: AccessType) => boolean;

  // Getter functions
  getRegionById: (id: string) => Region | undefined;
  getDistrictById: (id: string) => District | undefined;
  getAssemblyById: (id: string) => Assembly | undefined;
  getYouthById: (id: string) => Youth | undefined;
  getDailyProgramById: (id: string) => DailyProgram | undefined;
  getSermonById: (id: string) => Sermon | undefined;
  getProgramShareLinkById: (id: string) => ProgramShareLink | undefined;
  getProgramShareLinkByProgramId: (programId: string) => ProgramShareLink | undefined;

  // Filter functions
  getDistrictsByRegion: (regionId: string) => District[];
  getAssembliesByDistrict: (districtId: string) => Assembly[];
  getYouthsByAssembly: (assemblyId: string) => Youth[];
  getYouthsByDistrict: (districtId: string) => Youth[];
  getYouthsByRegion: (regionId: string) => Youth[];

  // Update functions
  updateYouth: (id: string, updates: Partial<Youth>) => Promise<Youth>;
  approvePermission: (id: string, approverId: string) => Promise<Permission>;

  // Utility functions
  clearStoredData: () => Promise<void>;
  refreshData: () => Promise<void>;
}

const SupabaseDataContext = createContext<DataContextType | undefined>(undefined);

export const SupabaseDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = useQueryClient();

  // Define queries for fetching data
  const regionsQuery = useQuery({
    queryKey: ['regions'],
    queryFn: services.regionsService.getAll
  });

  const districtsQuery = useQuery({
    queryKey: ['districts'],
    queryFn: services.districtsService.getAll
  });

  const assembliesQuery = useQuery({
    queryKey: ['assemblies'],
    queryFn: services.assembliesService.getAll
  });

  const youthsQuery = useQuery({
    queryKey: ['youths'],
    queryFn: services.youthsService.getAll
  });

  const paymentsQuery = useQuery({
    queryKey: ['payments'],
    queryFn: services.paymentsService.getAll
  });

  const permissionsQuery = useQuery({
    queryKey: ['permissions'],
    queryFn: services.permissionsService.getAll
  });

  const sermonsQuery = useQuery({
    queryKey: ['sermons'],
    queryFn: services.sermonsService.getAll
  });

  const dailyProgramsQuery = useQuery({
    queryKey: ['dailyPrograms'],
    queryFn: services.dailyProgramsService.getAll
  });

  const registrationLinksQuery = useQuery({
    queryKey: ['registrationLinks'],
    queryFn: services.registrationLinksService.getAll
  });

  const programShareLinksQuery = useQuery({
    queryKey: ['programShareLinks'],
    queryFn: services.programShareLinksService.getAll
  });

  const accessRightsQuery = useQuery({
    queryKey: ['accessRights'],
    queryFn: services.accessRightsService.getAll
  });

  const magicLinksQuery = useQuery({
    queryKey: ['magicLinks'],
    queryFn: services.magicLinksService.getAll
  });

  const accessLogsQuery = useQuery({
    queryKey: ['accessLogs'],
    queryFn: services.accessLogsService.getAll
  });

  // Define mutations for updating data
  const addRegionMutation = useMutation({
    mutationFn: (name: string) => services.regionsService.create({ name }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['regions'] });
    }
  });

  const updateRegionMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<Region, 'id'>> }) =>
      services.regionsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['regions'] });
    }
  });

  const deleteRegionMutation = useMutation({
    mutationFn: (id: string) => services.regionsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['regions'] });
    }
  });

  const addDistrictMutation = useMutation({
    mutationFn: ({ name, regionId }: { name: string, regionId: string }) =>
      services.districtsService.create({ name, regionId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['districts'] });
    }
  });

  const updateDistrictMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<District, 'id'>> }) =>
      services.districtsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['districts'] });
    }
  });

  const deleteDistrictMutation = useMutation({
    mutationFn: (id: string) => services.districtsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['districts'] });
    }
  });

  const addAssemblyMutation = useMutation({
    mutationFn: ({ name, districtId }: { name: string, districtId: string }) =>
      services.assembliesService.create({ name, districtId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assemblies'] });
    }
  });

  const updateAssemblyMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<Assembly, 'id'>> }) =>
      services.assembliesService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assemblies'] });
    }
  });

  const deleteAssemblyMutation = useMutation({
    mutationFn: (id: string) => services.assembliesService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assemblies'] });
    }
  });

  const addYouthMutation = useMutation({
    mutationFn: (youth: Omit<Youth, 'id' | 'payments' | 'registrationDate'>) =>
      services.youthsService.create({
        ...youth,
        registrationDate: new Date()
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['youths'] });
    }
  });

  const updateYouthMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Youth> }) =>
      services.youthsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['youths'] });
    }
  });

  const deleteYouthMutation = useMutation({
    mutationFn: (id: string) => services.youthsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['youths'] });
    }
  });



  const addPaymentMutation = useMutation({
    mutationFn: (payment: Omit<Payment, 'id'>) =>
      services.paymentsService.create(payment),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['youths'] });
    }
  });

  const updatePaymentMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<Payment, 'id'>> }) =>
      services.paymentsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['youths'] });
    }
  });

  const deletePaymentMutation = useMutation({
    mutationFn: (id: string) => services.paymentsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['youths'] });
    }
  });

  const addPermissionMutation = useMutation({
    mutationFn: (permission: Omit<Permission, 'id'>) =>
      services.permissionsService.create(permission),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
    }
  });

  const approvePermissionMutation = useMutation({
    mutationFn: ({ id, approverId }: { id: string, approverId: string }) =>
      services.permissionsService.approve(id, approverId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['permissions'] });
    }
  });

  const addSermonMutation = useMutation({
    mutationFn: (sermon: Omit<Sermon, 'id'>) =>
      services.sermonsService.create(sermon),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sermons'] });
    }
  });

  const updateSermonMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<Sermon, 'id'>> }) =>
      services.sermonsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sermons'] });
    }
  });

  const addDailyProgramMutation = useMutation({
    mutationFn: (program: Omit<DailyProgram, 'id'>) =>
      services.dailyProgramsService.create(program),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dailyPrograms'] });
    }
  });

  const createRegistrationLinkMutation = useMutation({
    mutationFn: (link: Omit<RegistrationLink, 'id'>) =>
      services.registrationLinksService.create(link),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['registrationLinks'] });
    }
  });

  const deleteRegistrationLinkMutation = useMutation({
    mutationFn: (id: string) =>
      services.registrationLinksService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['registrationLinks'] });
    }
  });

  const incrementRegistrationCountMutation = useMutation({
    mutationFn: (id: string) =>
      services.registrationLinksService.incrementRegistrationCount(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['registrationLinks'] });
    }
  });

  const createProgramShareLinkMutation = useMutation({
    mutationFn: (link: Omit<ProgramShareLink, 'id'>) =>
      services.programShareLinksService.create(link),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['programShareLinks'] });
    }
  });

  const incrementProgramShareViewCountMutation = useMutation({
    mutationFn: (id: string) =>
      services.programShareLinksService.incrementViewCount(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['programShareLinks'] });
    }
  });

  const createAccessRightMutation = useMutation({
    mutationFn: (accessRight: Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>) =>
      services.accessRightsService.create(accessRight),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accessRights'] });
    }
  });

  const updateAccessRightMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>> }) =>
      services.accessRightsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accessRights'] });
    }
  });

  const deleteAccessRightMutation = useMutation({
    mutationFn: (id: string) =>
      services.accessRightsService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accessRights'] });
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const createMagicLinkMutation = useMutation({
    mutationFn: (magicLink: Omit<MagicLink, 'id' | 'createdAt'>) =>
      services.magicLinksService.create(magicLink),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const updateMagicLinkMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<MagicLink, 'id' | 'createdAt'>> }) =>
      services.magicLinksService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const revokeMagicLinkMutation = useMutation({
    mutationFn: (id: string) =>
      services.magicLinksService.revoke(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const deleteMagicLinkMutation = useMutation({
    mutationFn: (id: string) =>
      services.magicLinksService.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const incrementMagicLinkViewCountMutation = useMutation({
    mutationFn: (id: string) =>
      services.magicLinksService.incrementViewCount(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const incrementMagicLinkEditCountMutation = useMutation({
    mutationFn: (id: string) =>
      services.magicLinksService.incrementEditCount(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
    }
  });

  const addAccessLogMutation = useMutation({
    mutationFn: (log: Omit<AccessLog, 'id' | 'timestamp' | 'createdAt'>) =>
      services.accessLogsService.create(log),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['accessLogs'] });
    }
  });

  const updateDailyProgramMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<DailyProgram, 'id'>> }) =>
      services.dailyProgramsService.update(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dailyPrograms'] });
    }
  });

  const updateProgramActivitiesMutation = useMutation({
    mutationFn: ({ programId, activities }: { programId: string, activities: ProgramActivity[] }) =>
      services.programActivitiesService.updateActivities(programId, activities),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dailyPrograms'] });
    }
  });

  // Provide data and functions to the context
  const contextValue: DataContextType = {
    // Data
    regions: regionsQuery.data || [],
    districts: districtsQuery.data || [],
    assemblies: assembliesQuery.data || [],
    youths: youthsQuery.data || [],
    payments: paymentsQuery.data || [],
    permissions: permissionsQuery.data || [],
    sermons: sermonsQuery.data || [],
    dailyPrograms: dailyProgramsQuery.data || [],
    registrationLinks: registrationLinksQuery.data || [],
    programShareLinks: programShareLinksQuery.data || [],
    accessRights: accessRightsQuery.data || [],
    magicLinks: magicLinksQuery.data || [],
    accessLogs: accessLogsQuery.data || [],

    // Loading states
    isLoading: {
      regions: regionsQuery.isLoading,
      districts: districtsQuery.isLoading,
      assemblies: assembliesQuery.isLoading,
      youths: youthsQuery.isLoading,
      payments: paymentsQuery.isLoading,
      permissions: permissionsQuery.isLoading,
      sermons: sermonsQuery.isLoading,
      dailyPrograms: dailyProgramsQuery.isLoading,
      registrationLinks: registrationLinksQuery.isLoading,
      programShareLinks: programShareLinksQuery.isLoading,
      accessRights: accessRightsQuery.isLoading,
      magicLinks: magicLinksQuery.isLoading,
      accessLogs: accessLogsQuery.isLoading
    },

    // Error states
    errors: {
      regions: regionsQuery.error as Error | null,
      districts: districtsQuery.error as Error | null,
      assemblies: assembliesQuery.error as Error | null,
      youths: youthsQuery.error as Error | null,
      payments: paymentsQuery.error as Error | null,
      permissions: permissionsQuery.error as Error | null,
      sermons: sermonsQuery.error as Error | null,
      dailyPrograms: dailyProgramsQuery.error as Error | null,
      registrationLinks: registrationLinksQuery.error as Error | null,
      programShareLinks: programShareLinksQuery.error as Error | null,
      accessRights: accessRightsQuery.error as Error | null,
      magicLinks: magicLinksQuery.error as Error | null,
      accessLogs: accessLogsQuery.error as Error | null
    },

    // CRUD operations
    addRegion: async (name: string) => {
      const result = await addRegionMutation.mutateAsync(name);
      return result;
    },

    updateRegion: async (id: string, updates: Partial<Omit<Region, 'id'>>) => {
      const result = await updateRegionMutation.mutateAsync({ id, updates });
      return result;
    },

    deleteRegion: async (id: string) => {
      await deleteRegionMutation.mutateAsync(id);
    },

    addDistrict: async (name: string, regionId: string) => {
      const result = await addDistrictMutation.mutateAsync({ name, regionId });
      return result;
    },

    updateDistrict: async (id: string, updates: Partial<Omit<District, 'id'>>) => {
      const result = await updateDistrictMutation.mutateAsync({ id, updates });
      return result;
    },

    deleteDistrict: async (id: string) => {
      await deleteDistrictMutation.mutateAsync(id);
    },

    addAssembly: async (name: string, districtId: string) => {
      const result = await addAssemblyMutation.mutateAsync({ name, districtId });
      return result;
    },

    updateAssembly: async (id: string, updates: Partial<Omit<Assembly, 'id'>>) => {
      const result = await updateAssemblyMutation.mutateAsync({ id, updates });
      return result;
    },

    deleteAssembly: async (id: string) => {
      await deleteAssemblyMutation.mutateAsync(id);
    },

    addYouth: async (youth: Omit<Youth, 'id' | 'payments' | 'registrationDate'>) => {
      const result = await addYouthMutation.mutateAsync(youth);
      return result;
    },

    updateYouth: async (id: string, updates: Partial<Youth>) => {
      const result = await updateYouthMutation.mutateAsync({ id, updates });
      return result;
    },

    deleteYouth: async (id: string) => {
      await deleteYouthMutation.mutateAsync(id);
    },

    addPayment: async (payment: Omit<Payment, 'id'>) => {
      const result = await addPaymentMutation.mutateAsync(payment);
      return result;
    },

    updatePayment: async (id: string, updates: Partial<Omit<Payment, 'id'>>) => {
      const result = await updatePaymentMutation.mutateAsync({ id, updates });
      return result;
    },

    deletePayment: async (id: string) => {
      await deletePaymentMutation.mutateAsync(id);
    },

    addPermission: async (permission: Omit<Permission, 'id'>) => {
      const result = await addPermissionMutation.mutateAsync(permission);
      return result;
    },

    approvePermission: async (id: string, approverId: string) => {
      const result = await approvePermissionMutation.mutateAsync({ id, approverId });
      return result;
    },

    addSermon: async (sermon: Omit<Sermon, 'id'>) => {
      const result = await addSermonMutation.mutateAsync(sermon);
      return result;
    },

    updateSermon: async (id: string, updates: Partial<Omit<Sermon, 'id'>>) => {
      const result = await updateSermonMutation.mutateAsync({ id, updates });
      return result;
    },

    addDailyProgram: async (program: Omit<DailyProgram, 'id'>) => {
      const result = await addDailyProgramMutation.mutateAsync(program);
      return result;
    },

    updateDailyProgram: async (id: string, updates: Partial<Omit<DailyProgram, 'id'>>) => {
      const result = await updateDailyProgramMutation.mutateAsync({ id, updates });
      return result;
    },

    updateProgramActivities: async (programId: string, activities: ProgramActivity[]) => {
      await updateProgramActivitiesMutation.mutateAsync({ programId, activities });
    },
    createRegistrationLink: async () => {
      const url = `/register/${uuidv4()}`;
      const result = await createRegistrationLinkMutation.mutateAsync({
        url,
        currentRegistrations: 0,
        maxRegistrations: null,
        expiresAt: null
      });
      return result;
    },

    deleteRegistrationLink: async (id: string) => {
      await deleteRegistrationLinkMutation.mutateAsync(id);
    },

    incrementRegistrationCount: async (linkIdentifier: string) => {
      await incrementRegistrationCountMutation.mutateAsync(linkIdentifier);
    },

    createProgramShareLink: async (programId: string) => {
      const url = `/program/share/${uuidv4()}`;
      const result = await createProgramShareLinkMutation.mutateAsync({
        programId,
        url,
        viewCount: 0
      });
      return result;
    },

    incrementProgramShareViewCount: async (linkId: string) => {
      await incrementProgramShareViewCountMutation.mutateAsync(linkId);
    },

    createAccessRight: async (accessRight: Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>) => {
      const result = await createAccessRightMutation.mutateAsync(accessRight);
      return result;
    },

    updateAccessRight: async (id: string, updates: Partial<Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>>) => {
      const result = await updateAccessRightMutation.mutateAsync({ id, updates });
      return result;
    },

    deleteAccessRight: async (id: string) => {
      await deleteAccessRightMutation.mutateAsync(id);
    },

    getAccessRightById: (id: string) => accessRightsQuery.data?.find(right => right.id === id),

    createMagicLink: async (accessRightId: string, name: string, expiresAt?: Date) => {
      const url = `/access/${uuidv4()}`;
      const result = await createMagicLinkMutation.mutateAsync({
        accessRightId,
        name,
        url,
        expiresAt,
        lastUsedAt: null,
        viewCount: 0,
        editCount: 0,
        active: true
      });
      return result;
    },

    updateMagicLink: async (id: string, updates: Partial<Omit<MagicLink, 'id' | 'createdAt'>>) => {
      const result = await updateMagicLinkMutation.mutateAsync({ id, updates });
      return result;
    },

    revokeMagicLink: async (id: string) => {
      await revokeMagicLinkMutation.mutateAsync(id);
    },

    deleteMagicLink: async (id: string) => {
      await deleteMagicLinkMutation.mutateAsync(id);
    },

    getMagicLinkById: (id: string) => magicLinksQuery.data?.find(link => link.id === id),

    getMagicLinksByAccessRightId: (accessRightId: string) =>
      magicLinksQuery.data?.filter(link => link.accessRightId === accessRightId) || [],

    incrementMagicLinkViewCount: async (id: string) => {
      await incrementMagicLinkViewCountMutation.mutateAsync(id);
    },

    incrementMagicLinkEditCount: async (id: string) => {
      await incrementMagicLinkEditCountMutation.mutateAsync(id);
    },

    addAccessLog: async (log: Omit<AccessLog, 'id' | 'timestamp' | 'createdAt'>) => {
      await addAccessLogMutation.mutateAsync(log);
    },

    getAccessLogsByMagicLinkId: (magicLinkId: string) =>
      accessLogsQuery.data?.filter(log => log.magicLinkId === magicLinkId) || [],

    checkAccess: (magicLinkId: string, feature: FeatureType, accessType: AccessType) => {
      const magicLink = magicLinksQuery.data?.find(link => link.id === magicLinkId);
      if (!magicLink || !magicLink.active) return false;

      // Check if the magic link has expired
      if (magicLink.expiresAt && new Date() > magicLink.expiresAt) return false;

      const accessRight = accessRightsQuery.data?.find(right => right.id === magicLink.accessRightId);
      if (!accessRight) return false;

      // Check if the feature is allowed
      const featureAccess = accessRight.features.find((f: any) => f.feature === feature);
      if (!featureAccess) return false;

      // Check if the access type is allowed
      if (accessType === 'read') {
        return featureAccess.access === 'read' || featureAccess.access === 'read-write';
      } else if (accessType === 'write') {
        return featureAccess.access === 'read-write';
      }

      return false;
    },
    getRegionById: (id: string) => regionsQuery.data?.find(region => region.id === id),
    getDistrictById: (id: string) => districtsQuery.data?.find(district => district.id === id),
    getAssemblyById: (id: string) => assembliesQuery.data?.find(assembly => assembly.id === id),
    getYouthById: (id: string) => youthsQuery.data?.find(youth => youth.id === id),
    getDailyProgramById: (id: string) => dailyProgramsQuery.data?.find(program => program.id === id),
    getSermonById: (id: string) => sermonsQuery.data?.find(sermon => sermon.id === id),
    getProgramShareLinkById: (id: string) => programShareLinksQuery.data?.find(link => link.id === id),
    getProgramShareLinkByProgramId: (programId: string) => programShareLinksQuery.data?.find(link => link.programId === programId),

    getDistrictsByRegion: (regionId: string) =>
      districtsQuery.data?.filter(district => district.regionId === regionId) || [],

    getAssembliesByDistrict: (districtId: string) =>
      assembliesQuery.data?.filter(assembly => assembly.districtId === districtId) || [],

    getYouthsByAssembly: (assemblyId: string) =>
      youthsQuery.data?.filter(youth => youth.assemblyId === assemblyId) || [],

    getYouthsByDistrict: (districtId: string) => {
      const assemblies = assembliesQuery.data?.filter(assembly => assembly.districtId === districtId) || [];
      const assemblyIds = assemblies.map(assembly => assembly.id);
      return youthsQuery.data?.filter(youth => assemblyIds.includes(youth.assemblyId)) || [];
    },

    getYouthsByRegion: (regionId: string) => {
      const districts = districtsQuery.data?.filter(district => district.regionId === regionId) || [];
      const districtIds = districts.map(district => district.id);
      const assemblies = assembliesQuery.data?.filter(assembly => districtIds.includes(assembly.districtId)) || [];
      const assemblyIds = assemblies.map(assembly => assembly.id);
      return youthsQuery.data?.filter(youth => assemblyIds.includes(youth.assemblyId)) || [];
    },

    clearStoredData: async () => {
      // This is a no-op for Supabase since data is stored in the database
      console.log('clearStoredData is a no-op for Supabase');
    },

    refreshData: async () => {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['regions'] }),
        queryClient.invalidateQueries({ queryKey: ['districts'] }),
        queryClient.invalidateQueries({ queryKey: ['assemblies'] }),
        queryClient.invalidateQueries({ queryKey: ['youths'] }),
        queryClient.invalidateQueries({ queryKey: ['payments'] }),
        queryClient.invalidateQueries({ queryKey: ['permissions'] }),
        queryClient.invalidateQueries({ queryKey: ['sermons'] }),
        queryClient.invalidateQueries({ queryKey: ['dailyPrograms'] }),
        queryClient.invalidateQueries({ queryKey: ['registrationLinks'] }),
        queryClient.invalidateQueries({ queryKey: ['programShareLinks'] }),
        queryClient.invalidateQueries({ queryKey: ['accessRights'] }),
        queryClient.invalidateQueries({ queryKey: ['magicLinks'] }),
        queryClient.invalidateQueries({ queryKey: ['accessLogs'] })
      ]);
    }
  };

  return (
    <SupabaseDataContext.Provider value={contextValue}>
      {children}
    </SupabaseDataContext.Provider>
  );
};

export const useSupabaseData = () => {
  const context = useContext(SupabaseDataContext);
  if (context === undefined) {
    throw new Error('useSupabaseData must be used within a SupabaseDataProvider');
  }
  return context;
};
