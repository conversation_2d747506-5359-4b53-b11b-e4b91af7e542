import { supabase } from '@/lib/supabase';
import {
  Assembly,
  District,
  Payment,
  Region,
  Youth
} from '@/types';

// Import helper functions
import {
  convertKeysToCamel,
  convertKeysToSnake
} from './supabase.service.helpers';

// Regions Service
export const regionsService = {
  async getAll(): Promise<Region[]> {
    const { data, error } = await supabase
      .from('regions')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching regions:', error);
      throw error;
    }

    return data.map(region => convertKeysToCamel(region)) as Region[];
  },

  async getById(id: string): Promise<Region | null> {
    const { data, error } = await supabase
      .from('regions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching region with id ${id}:`, error);
      throw error;
    }

    return data ? convertKeysToCamel(data) as Region : null;
  },

  async create(region: Omit<Region, 'id'>): Promise<Region> {
    const { data, error } = await supabase
      .from('regions')
      .insert(convertKeysToSnake(region))
      .select()
      .single();

    if (error) {
      console.error('Error creating region:', error);
      throw error;
    }

    return convertKeysToCamel(data) as Region;
  },

  async update(id: string, region: Partial<Omit<Region, 'id'>>): Promise<Region> {
    const { data, error } = await supabase
      .from('regions')
      .update(convertKeysToSnake(region))
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating region with id ${id}:`, error);
      throw error;
    }

    return convertKeysToCamel(data) as Region;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('regions')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting region with id ${id}:`, error);
      throw error;
    }
  }
};

// Districts Service
export const districtsService = {
  async getAll(): Promise<District[]> {
    const { data, error } = await supabase
      .from('districts')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching districts:', error);
      throw error;
    }

    return data.map(district => convertKeysToCamel(district)) as District[];
  },

  async getById(id: string): Promise<District | null> {
    const { data, error } = await supabase
      .from('districts')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching district with id ${id}:`, error);
      throw error;
    }

    return data ? convertKeysToCamel(data) as District : null;
  },

  async getByRegion(regionId: string): Promise<District[]> {
    const { data, error } = await supabase
      .from('districts')
      .select('*')
      .eq('region_id', regionId)
      .order('name');

    if (error) {
      console.error(`Error fetching districts for region ${regionId}:`, error);
      throw error;
    }

    return data.map(district => convertKeysToCamel(district)) as District[];
  },

  async create(district: Omit<District, 'id'>): Promise<District> {
    const { data, error } = await supabase
      .from('districts')
      .insert(convertKeysToSnake(district))
      .select()
      .single();

    if (error) {
      console.error('Error creating district:', error);
      throw error;
    }

    return convertKeysToCamel(data) as District;
  },

  async update(id: string, district: Partial<Omit<District, 'id'>>): Promise<District> {
    const { data, error } = await supabase
      .from('districts')
      .update(convertKeysToSnake(district))
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating district with id ${id}:`, error);
      throw error;
    }

    return convertKeysToCamel(data) as District;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('districts')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting district with id ${id}:`, error);
      throw error;
    }
  }
};

// Assemblies Service
export const assembliesService = {
  async getAll(): Promise<Assembly[]> {
    const { data, error } = await supabase
      .from('assemblies')
      .select('*')
      .order('name');

    if (error) {
      console.error('Error fetching assemblies:', error);
      throw error;
    }

    return data.map(assembly => convertKeysToCamel(assembly)) as Assembly[];
  },

  async getById(id: string): Promise<Assembly | null> {
    const { data, error } = await supabase
      .from('assemblies')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching assembly with id ${id}:`, error);
      throw error;
    }

    return data ? convertKeysToCamel(data) as Assembly : null;
  },

  async getByDistrict(districtId: string): Promise<Assembly[]> {
    const { data, error } = await supabase
      .from('assemblies')
      .select('*')
      .eq('district_id', districtId)
      .order('name');

    if (error) {
      console.error(`Error fetching assemblies for district ${districtId}:`, error);
      throw error;
    }

    return data.map(assembly => convertKeysToCamel(assembly)) as Assembly[];
  },

  async create(assembly: Omit<Assembly, 'id'>): Promise<Assembly> {
    const { data, error } = await supabase
      .from('assemblies')
      .insert(convertKeysToSnake(assembly))
      .select()
      .single();

    if (error) {
      console.error('Error creating assembly:', error);
      throw error;
    }

    return convertKeysToCamel(data) as Assembly;
  },

  async update(id: string, assembly: Partial<Omit<Assembly, 'id'>>): Promise<Assembly> {
    const { data, error } = await supabase
      .from('assemblies')
      .update(convertKeysToSnake(assembly))
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating assembly with id ${id}:`, error);
      throw error;
    }

    return convertKeysToCamel(data) as Assembly;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('assemblies')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting assembly with id ${id}:`, error);
      throw error;
    }
  }
};

// Youths Service
export const youthsService = {
  async getAll(): Promise<Youth[]> {
    console.log('Fetching youths from Supabase...');

    const { data, error } = await supabase
      .from('youths')
      .select('*')
      .order('last_name');

    if (error) {
      console.error('Error fetching youths:', error);
      throw error;
    }

    console.log('Raw youths data from Supabase:', data);

    if (!data || data.length === 0) {
      console.warn('No youths found in Supabase database');
      return [];
    }

    const mappedData = data.map(youth => {
      const converted = convertKeysToCamel(youth);
      return {
        ...converted,
        registrationDate: new Date(converted.registrationDate),
        payments: [] // Payments will be fetched separately
      } as Youth;
    });

    console.log('Mapped youths data:', mappedData);

    return mappedData;
  },

  async getById(id: string): Promise<Youth | null> {
    const { data, error } = await supabase
      .from('youths')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching youth with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    // Fetch payments for this youth
    const { data: paymentsData, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('youth_id', id);

    if (paymentsError) {
      console.error(`Error fetching payments for youth ${id}:`, paymentsError);
      throw paymentsError;
    }

    const payments = paymentsData.map(payment => {
      const convertedPayment = convertKeysToCamel(payment);
      return {
        ...convertedPayment,
        date: new Date(convertedPayment.date)
      };
    });

    return {
      ...converted,
      registrationDate: new Date(converted.registrationDate),
      payments
    } as Youth;
  },

  async getByAssembly(assemblyId: string): Promise<Youth[]> {
    const { data, error } = await supabase
      .from('youths')
      .select('*')
      .eq('assembly_id', assemblyId)
      .order('last_name');

    if (error) {
      console.error(`Error fetching youths for assembly ${assemblyId}:`, error);
      throw error;
    }

    return data.map(youth => {
      const converted = convertKeysToCamel(youth);
      return {
        ...converted,
        registrationDate: new Date(converted.registrationDate),
        payments: [] // Payments will be fetched separately
      } as Youth;
    });
  },

  async getByDistrict(districtId: string): Promise<Youth[]> {
    // First get all assemblies in this district
    const { data: assemblies, error: assembliesError } = await supabase
      .from('assemblies')
      .select('id')
      .eq('district_id', districtId);

    if (assembliesError) {
      console.error(`Error fetching assemblies for district ${districtId}:`, assembliesError);
      throw assembliesError;
    }

    const assemblyIds = assemblies.map(a => a.id);

    if (assemblyIds.length === 0) {
      return [];
    }

    // Then get all youths in these assemblies
    const { data, error } = await supabase
      .from('youths')
      .select('*')
      .in('assembly_id', assemblyIds)
      .order('last_name');

    if (error) {
      console.error(`Error fetching youths for district ${districtId}:`, error);
      throw error;
    }

    return data.map(youth => {
      const converted = convertKeysToCamel(youth);
      return {
        ...converted,
        registrationDate: new Date(converted.registrationDate),
        payments: [] // Payments will be fetched separately
      } as Youth;
    });
  },

  async getByRegion(regionId: string): Promise<Youth[]> {
    // First get all districts in this region
    const { data: districts, error: districtsError } = await supabase
      .from('districts')
      .select('id')
      .eq('region_id', regionId);

    if (districtsError) {
      console.error(`Error fetching districts for region ${regionId}:`, districtsError);
      throw districtsError;
    }

    const districtIds = districts.map(d => d.id);

    if (districtIds.length === 0) {
      return [];
    }

    // Then get all assemblies in these districts
    const { data: assemblies, error: assembliesError } = await supabase
      .from('assemblies')
      .select('id')
      .in('district_id', districtIds);

    if (assembliesError) {
      console.error(`Error fetching assemblies for districts in region ${regionId}:`, assembliesError);
      throw assembliesError;
    }

    const assemblyIds = assemblies.map(a => a.id);

    if (assemblyIds.length === 0) {
      return [];
    }

    // Finally get all youths in these assemblies
    const { data, error } = await supabase
      .from('youths')
      .select('*')
      .in('assembly_id', assemblyIds)
      .order('last_name');

    if (error) {
      console.error(`Error fetching youths for region ${regionId}:`, error);
      throw error;
    }

    return data.map(youth => {
      const converted = convertKeysToCamel(youth);
      return {
        ...converted,
        registrationDate: new Date(converted.registrationDate),
        payments: [] // Payments will be fetched separately
      } as Youth;
    });
  },

  async create(youth: Omit<Youth, 'id' | 'payments'>): Promise<Youth> {
    const youthToInsert = {
      ...convertKeysToSnake(youth),
      registration_date: youth.registrationDate.toISOString()
    };

    const { data, error } = await supabase
      .from('youths')
      .insert(youthToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating youth:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      registrationDate: new Date(converted.registrationDate),
      payments: []
    } as Youth;
  },

  async update(id: string, youth: Partial<Omit<Youth, 'id' | 'payments'>>): Promise<Youth> {
    const youthToUpdate: any = convertKeysToSnake(youth);

    if (youth.registrationDate) {
      youthToUpdate.registration_date = youth.registrationDate.toISOString();
    }

    const { data, error } = await supabase
      .from('youths')
      .update(youthToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating youth with id ${id}:`, error);
      throw error;
    }

    // Fetch payments for this youth
    const { data: paymentsData, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('youth_id', id);

    if (paymentsError) {
      console.error(`Error fetching payments for youth ${id}:`, paymentsError);
      throw paymentsError;
    }

    const payments = paymentsData.map(payment => {
      const convertedPayment = convertKeysToCamel(payment);
      return {
        ...convertedPayment,
        date: new Date(convertedPayment.date)
      };
    });

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      registrationDate: new Date(converted.registrationDate),
      payments
    } as Youth;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('youths')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting youth with id ${id}:`, error);
      throw error;
    }
  }
};

// Payments Service
export const paymentsService = {
  async getAll(): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .order('date', { ascending: false });

    if (error) {
      console.error('Error fetching payments:', error);
      throw error;
    }

    return data.map(payment => {
      const converted = convertKeysToCamel(payment);
      return {
        ...converted,
        date: new Date(converted.date)
      } as Payment;
    });
  },

  async getById(id: string): Promise<Payment | null> {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching payment with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date)
    } as Payment;
  },

  async getByYouth(youthId: string): Promise<Payment[]> {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('youth_id', youthId)
      .order('date', { ascending: false });

    if (error) {
      console.error(`Error fetching payments for youth ${youthId}:`, error);
      throw error;
    }

    return data.map(payment => {
      const converted = convertKeysToCamel(payment);
      return {
        ...converted,
        date: new Date(converted.date)
      } as Payment;
    });
  },

  async create(payment: Omit<Payment, 'id'>): Promise<Payment> {
    const paymentToInsert = {
      ...convertKeysToSnake(payment),
      date: payment.date.toISOString()
    };

    const { data, error } = await supabase
      .from('payments')
      .insert(paymentToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating payment:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date)
    } as Payment;
  },

  async update(id: string, payment: Partial<Omit<Payment, 'id'>>): Promise<Payment> {
    const paymentToUpdate: any = convertKeysToSnake(payment);

    if (payment.date) {
      paymentToUpdate.date = payment.date.toISOString();
    }

    const { data, error } = await supabase
      .from('payments')
      .update(paymentToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating payment with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date)
    } as Payment;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('payments')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting payment with id ${id}:`, error);
      throw error;
    }
  }
};
