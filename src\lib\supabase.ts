import { Database } from '@/types/database.types';
import { createClient } from '@supabase/supabase-js';

// Utiliser les variables d'environnement ou des valeurs par défaut
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://xrppmbmrouvnopmdaxzj.supabase.co';
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhycHBtYm1yb3V2bm9wbWRheHpqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3Mzk5NDQsImV4cCI6MjA2MjMxNTk0NH0.pwCF6ijCJMjtwUOyUZc1vxfO4QfPQLQkUmOZQM1BsLg';

// Vérifier que les valeurs sont définies (utilisant les valeurs par défaut si nécessaire)
if (!supabaseUrl || !supabaseAnonKey) {
  console.error('Warning: Using fallback Supabase credentials. This should not happen in production.');
}

console.log('Initializing Supabase client with URL:', supabaseUrl);
export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Test the connection
(async () => {
  try {
    console.log('Testing Supabase connection with URL:', supabaseUrl);
    console.log('Anon key (first 10 chars):', supabaseAnonKey.substring(0, 10) + '...');

    // Try to get youth count to verify connection
    const { count, error } = await supabase.from('youths').select('*', { count: 'exact', head: true });

    if (error) {
      console.error('Error getting youth count from Supabase:', error);

      // Check if it's a permissions issue
      if (error.code === 'PGRST301' || error.message.includes('permission denied')) {
        console.error('This appears to be a Row Level Security (RLS) issue. Check your Supabase RLS policies.');
      }
    } else {
      console.log('Successfully connected to Supabase. Youth count:', count);
    }

    // Check connection to another table to verify database access
    const { data: regionsData, error: regionsError } = await supabase
      .from('regions')
      .select('*')
      .limit(1);

    if (regionsError) {
      console.error('Error accessing regions table:', regionsError);
    } else {
      console.log('Successfully accessed regions table');
    }
  } catch (err) {
    console.error('Exception when connecting to Supabase:', err);
  }
})();
