
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import { useTranslation } from 'react-i18next';

const RegistrationSuccess = () => {
  const { t } = useTranslation();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4">
      <div className="w-full max-w-md text-center">
        <div className="rounded-full bg-green-100 p-4 w-20 h-20 flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="h-10 w-10 text-green-600" />
        </div>
        <h1 className="text-2xl font-bold text-gray-800 mb-2">{t('registration.success.title')}</h1>
        <p className="text-gray-600 mb-8">
          {t('registration.success.message')}
        </p>
        <div className="space-y-4">
          <div className="p-4 bg-white rounded-lg shadow-sm border border-gray-200">
            <h3 className="font-medium text-gray-800 mb-2">{t('registration.success.whatNext')}</h3>
            <ul className="text-sm text-gray-600 text-left space-y-2">
              <li>• {t('registration.success.waitConfirmation')}</li>
              <li>• {t('registration.success.prepareCamp')}</li>
              <li>• {t('registration.success.contactOrganizer')}</li>
            </ul>
          </div>
          <Button className="w-full" onClick={() => window.close()}>
            {t('registration.success.closePage')}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default RegistrationSuccess;
