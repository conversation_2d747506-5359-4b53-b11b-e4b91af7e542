
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  RadioGroup,
  RadioGroupItem
} from "@/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { ArrowLeft } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const AddYouth = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const {
    regions,
    districts,
    assemblies,
    addYouth,
    getDistrictsByRegion,
    getAssembliesByDistrict,
  } = useSupabaseData();

  const [formState, setFormState] = useState({
    firstName: "",
    lastName: "",
    gender: "male" as "male" | "female",
    regionId: "",
    districtId: "",
    assemblyId: "",
  });

  const [filteredDistricts, setFilteredDistricts] = useState(districts);
  const [filteredAssemblies, setFilteredAssemblies] = useState(assemblies);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Update districts when region changes
  const handleRegionChange = (value: string) => {
    setFormState((prev) => ({
      ...prev,
      regionId: value,
      districtId: "",
      assemblyId: "",
    }));

    const districtsInRegion = getDistrictsByRegion(value);
    setFilteredDistricts(districtsInRegion);
    setFilteredAssemblies([]);
  };

  // Update assemblies when district changes
  const handleDistrictChange = (value: string) => {
    setFormState((prev) => ({
      ...prev,
      districtId: value,
      assemblyId: "",
    }));

    const assembliesInDistrict = getAssembliesByDistrict(value);
    setFilteredAssemblies(assembliesInDistrict);
  };

  // Form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    const newErrors: Record<string, string> = {};

    if (!formState.firstName.trim()) {
      newErrors.firstName = t('youths.firstNameRequired') || "Le prénom est requis";
    }

    if (!formState.lastName.trim()) {
      newErrors.lastName = t('youths.lastNameRequired') || "Le nom est requis";
    }

    if (!formState.assemblyId) {
      newErrors.assemblyId = t('youths.assemblyRequired') || "L'assemblée est requise";
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Add youth
    try {
      addYouth({
        firstName: formState.firstName,
        lastName: formState.lastName,
        gender: formState.gender,
        assemblyId: formState.assemblyId,
      });

      toast({
        title: t('common.success'),
        description: t('youths.addedSuccessfully') || "Le jeune a été ajouté avec succès",
      });

      navigate("/youths");
    } catch (error) {
      console.error("Error adding youth:", error);
      toast({
        title: t('common.error'),
        description: t('youths.errorAdding') || "Une erreur est survenue lors de l'ajout du jeune",
        variant: "destructive",
      });
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormState((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  return (
    <div>
      <div className="mb-6">
        <Button
          variant="outline"
          onClick={() => navigate("/youths")}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {t('common.back') || "Retour à la liste"}
        </Button>
        <h1 className="text-2xl font-bold">{t('youths.addYouth')}</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('youths.personalInfo')}</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="lastName">{t('common.lastName')}</Label>
                <Input
                  id="lastName"
                  name="lastName"
                  value={formState.lastName}
                  onChange={handleInputChange}
                  className={errors.lastName ? "border-destructive" : ""}
                />
                {errors.lastName && (
                  <p className="text-sm text-destructive">{errors.lastName}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="firstName">{t('common.firstName')}</Label>
                <Input
                  id="firstName"
                  name="firstName"
                  value={formState.firstName}
                  onChange={handleInputChange}
                  className={errors.firstName ? "border-destructive" : ""}
                />
                {errors.firstName && (
                  <p className="text-sm text-destructive">{errors.firstName}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label>{t('common.gender')}</Label>
              <RadioGroup
                value={formState.gender}
                onValueChange={(value) =>
                  setFormState((prev) => ({ ...prev, gender: value as "male" | "female" }))
                }
                className="flex gap-4"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="male" id="male" />
                  <Label htmlFor="male" className="cursor-pointer">{t('common.male')}</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="female" id="female" />
                  <Label htmlFor="female" className="cursor-pointer">{t('common.female')}</Label>
                </div>
              </RadioGroup>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="region">{t('sidebar.regions')}</Label>
                <Select onValueChange={handleRegionChange} value={formState.regionId}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('youths.selectRegion') || "Sélectionner une région"} />
                  </SelectTrigger>
                  <SelectContent>
                    {regions.map((region) => (
                      <SelectItem key={region.id} value={region.id}>
                        {region.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="district">{t('sidebar.districts')}</Label>
                <Select
                  onValueChange={handleDistrictChange}
                  value={formState.districtId}
                  disabled={!formState.regionId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t('youths.selectDistrict') || "Sélectionner un district"} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredDistricts.map((district) => (
                      <SelectItem key={district.id} value={district.id}>
                        {district.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="assembly">{t('sidebar.assemblies')}</Label>
                <Select
                  onValueChange={(value) =>
                    setFormState((prev) => ({ ...prev, assemblyId: value }))
                  }
                  value={formState.assemblyId}
                  disabled={!formState.districtId}
                >
                  <SelectTrigger className={errors.assemblyId ? "border-destructive" : ""}>
                    <SelectValue placeholder={t('youths.selectAssembly') || "Sélectionner une assemblée"} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredAssemblies.map((assembly) => (
                      <SelectItem key={assembly.id} value={assembly.id}>
                        {assembly.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.assemblyId && (
                  <p className="text-sm text-destructive">{errors.assemblyId}</p>
                )}
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit">{t('common.save')}</Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddYouth;
