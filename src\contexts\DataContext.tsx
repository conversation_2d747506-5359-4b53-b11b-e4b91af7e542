import { Assembly, DailyProgram, District, Payment, Permission, ProgramActivity, ProgramShareLink, Region, RegistrationLink, Sermon, Youth } from '@/types';
import React, { createContext, useContext, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

interface DataContextType {
  regions: Region[];
  districts: District[];
  assemblies: Assembly[];
  youths: Youth[];
  payments: Payment[];
  permissions: Permission[];
  sermons: Sermon[];
  dailyPrograms: DailyProgram[];
  registrationLinks: RegistrationLink[];
  programShareLinks: ProgramShareLink[];
  accessRights: AccessRight[];
  magicLinks: MagicLink[];
  accessLogs: AccessLog[];

  addRegion: (name: string) => void;
  addDistrict: (name: string, regionId: string) => void;
  addAssembly: (name: string, districtId: string) => void;
  addYouth: (youth: Omit<Youth, 'id' | 'payments' | 'registrationDate'>) => void;
  addPayment: (payment: Omit<Payment, 'id'>) => void;
  addPermission: (permission: Omit<Permission, 'id'>) => void;
  addSermon: (sermon: Omit<Sermon, 'id'>) => void;
  addDailyProgram: (program: Omit<DailyProgram, 'id'>) => void;
  updateDailyProgram: (id: string, updates: Partial<Omit<DailyProgram, 'id'>>) => void;
  updateProgramActivities: (programId: string, activities: ProgramActivity[]) => void;
  updateSermon: (id: string, updates: Partial<Omit<Sermon, 'id'>>) => void;
  createRegistrationLink: () => RegistrationLink;
  deleteRegistrationLink: (id: string) => void;
  incrementRegistrationCount: (linkId: string) => void;
  createProgramShareLink: (programId: string) => ProgramShareLink;
  getProgramShareLinkByProgramId: (programId: string) => ProgramShareLink | undefined;
  incrementProgramShareViewCount: (linkId: string) => void;
  clearStoredData: () => void;

  // Fonctions pour les droits d'accès
  createAccessRight: (accessRight: Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>) => AccessRight;
  updateAccessRight: (id: string, updates: Partial<Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>>) => void;
  deleteAccessRight: (id: string) => void;
  getAccessRightById: (id: string) => AccessRight | undefined;

  // Fonctions pour les liens magiques
  createMagicLink: (accessRightId: string, name: string, expiresAt?: Date) => MagicLink;
  updateMagicLink: (id: string, updates: Partial<Omit<MagicLink, 'id' | 'createdAt'>>) => void;
  revokeMagicLink: (id: string) => void;
  deleteMagicLink: (id: string) => void;
  getMagicLinkById: (id: string) => MagicLink | undefined;
  getMagicLinksByAccessRightId: (accessRightId: string) => MagicLink[];
  incrementMagicLinkViewCount: (id: string) => void;
  incrementMagicLinkEditCount: (id: string) => void;

  // Fonctions pour les logs d'accès
  addAccessLog: (log: Omit<AccessLog, 'id' | 'timestamp'>) => void;
  getAccessLogsByMagicLinkId: (magicLinkId: string) => AccessLog[];

  // Fonctions pour vérifier les droits d'accès
  checkAccess: (magicLinkId: string, feature: FeatureType, accessType: AccessType) => boolean;

  getRegionById: (id: string) => Region | undefined;
  getDistrictById: (id: string) => District | undefined;
  getAssemblyById: (id: string) => Assembly | undefined;
  getYouthById: (id: string) => Youth | undefined;
  getDailyProgramById: (id: string) => DailyProgram | undefined;
  getSermonById: (id: string) => Sermon | undefined;
  getProgramShareLinkById: (id: string) => ProgramShareLink | undefined;

  getDistrictsByRegion: (regionId: string) => District[];
  getAssembliesByDistrict: (districtId: string) => Assembly[];
  getYouthsByAssembly: (assemblyId: string) => Youth[];
  getYouthsByDistrict: (districtId: string) => Youth[];
  getYouthsByRegion: (regionId: string) => Youth[];

  updateYouth: (id: string, updates: Partial<Youth>) => void;
  approvePermission: (id: string, approverId: string) => void;
}

const DataContext = createContext<DataContextType | undefined>(undefined);

// Données mock pour le développement
const mockRegions: Region[] = [
  { id: '1', name: 'Région du Centre' },
  { id: '2', name: 'Région du Littoral' },
  { id: '3', name: 'Région de l\'Ouest' },
];

const mockDistricts: District[] = [
  { id: '1', name: 'Yaoundé Nord', regionId: '1' },
  { id: '2', name: 'Yaoundé Sud', regionId: '1' },
  { id: '3', name: 'Douala', regionId: '2' },
];

const mockAssemblies: Assembly[] = [
  { id: '1', name: 'Assemblée de Bastos', districtId: '1' },
  { id: '2', name: 'Assemblée de Mvog-Ada', districtId: '1' },
  { id: '3', name: 'Assemblée de Biyem-Assi', districtId: '2' },
];

// Mock youths data
const mockYouths: Youth[] = [];

export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [regions, setRegions] = useState<Region[]>(mockRegions);
  const [districts, setDistricts] = useState<District[]>(mockDistricts);
  const [assemblies, setAssemblies] = useState<Assembly[]>(mockAssemblies);

  // Initialize youths with mock data
  const [youths, setYouths] = useState<Youth[]>(mockYouths);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [sermons, setSermons] = useState<Sermon[]>([]);
  const [dailyPrograms, setDailyPrograms] = useState<DailyProgram[]>([]);
  const [programShareLinks, setProgramShareLinks] = useState<ProgramShareLink[]>([]);

  const [accessRights, setAccessRights] = useState<AccessRight[]>([]);

  const [magicLinks, setMagicLinks] = useState<MagicLink[]>([]);

  const [accessLogs, setAccessLogs] = useState<AccessLog[]>([]);
  // Initialize with default test registration links
  const [registrationLinks, setRegistrationLinks] = useState<RegistrationLink[]>([
    {
      id: 'test-link-id',
      url: '/register/test-link',
      currentRegistrations: 0,
      createdAt: new Date()
    },
    // Add a link with the specific ID from your example
    {
      id: 'specific-link-id',
      url: '/register/15baf29b-a909-4eff-a8bb-86378f1fbcc2',
      currentRegistrations: 0,
      createdAt: new Date()
    }
  ]);

  const addRegion = (name: string) => {
    const newRegion: Region = {
      id: uuidv4(),
      name
    };
    setRegions([...regions, newRegion]);
  };

  const addDistrict = (name: string, regionId: string) => {
    const newDistrict: District = {
      id: uuidv4(),
      name,
      regionId
    };
    setDistricts([...districts, newDistrict]);
  };

  const addAssembly = (name: string, districtId: string) => {
    const newAssembly: Assembly = {
      id: uuidv4(),
      name,
      districtId
    };
    setAssemblies([...assemblies, newAssembly]);
  };

  const addYouth = (youth: Omit<Youth, 'id' | 'payments' | 'registrationDate'>) => {
    console.log("Adding new youth:", youth);

    const newYouth: Youth = {
      id: uuidv4(),
      ...youth,
      payments: [],
      registrationDate: new Date()
    };

    // Add the new youth to the state
    setYouths(currentYouths => {
      console.log("Current youths before adding:", currentYouths);
      const updatedYouths = [...currentYouths, newYouth];
      console.log("Updated youths after adding:", updatedYouths);
      return updatedYouths;
    });
  };

  const addPayment = (payment: Omit<Payment, 'id'>) => {
    const newPayment: Payment = {
      id: uuidv4(),
      ...payment
    };
    setPayments([...payments, newPayment]);

    // Mettre à jour les paiements dans l'objet youth correspondant
    setYouths(youths.map(youth => {
      if (youth.id === payment.youthId) {
        return {
          ...youth,
          payments: [...youth.payments, newPayment]
        };
      }
      return youth;
    }));
  };

  const addPermission = (permission: Omit<Permission, 'id'>) => {
    const newPermission: Permission = {
      id: uuidv4(),
      ...permission
    };
    setPermissions([...permissions, newPermission]);
  };

  const addSermon = (sermon: Omit<Sermon, 'id'>) => {
    const newSermon: Sermon = {
      id: uuidv4(),
      ...sermon
    };
    setSermons([...sermons, newSermon]);
  };

  const updateSermon = (id: string, updates: Partial<Omit<Sermon, 'id'>>) => {
    setSermons(sermons.map(sermon => {
      if (sermon.id === id) {
        return {
          ...sermon,
          ...updates,
          // Si la date est fournie comme string, la convertir en Date
          date: updates.date instanceof Date ? updates.date :
            updates.date ? new Date(updates.date) : sermon.date
        };
      }
      return sermon;
    }));
  };

  const getSermonById = (id: string) => sermons.find(sermon => sermon.id === id);

  const addDailyProgram = (program: Omit<DailyProgram, 'id'>) => {
    const newProgram: DailyProgram = {
      id: uuidv4(),
      ...program
    };
    setDailyPrograms([...dailyPrograms, newProgram]);
  };

  const updateDailyProgram = (id: string, updates: Partial<Omit<DailyProgram, 'id'>>) => {
    setDailyPrograms(dailyPrograms.map(program => {
      if (program.id === id) {
        return {
          ...program,
          ...updates,
          // Si la date est fournie comme string, la convertir en Date
          date: updates.date instanceof Date ? updates.date :
            updates.date ? new Date(updates.date) : program.date
        };
      }
      return program;
    }));
  };

  const getDailyProgramById = (id: string) => dailyPrograms.find(program => program.id === id);

  const updateProgramActivities = (programId: string, activities: ProgramActivity[]) => {
    setDailyPrograms(dailyPrograms.map(program => {
      if (program.id === programId) {
        return {
          ...program,
          activities
        };
      }
      return program;
    }));
  };

  const createProgramShareLink = (programId: string) => {
    console.log('Creating program share link for programId:', programId);

    // Check if a link already exists for this program
    const existingLink = programShareLinks.find(link => link.programId === programId);
    if (existingLink) {
      console.log('Found existing link:', existingLink);
      return existingLink;
    }

    // Create a new link
    const linkId = uuidv4();
    const newLink: ProgramShareLink = {
      id: linkId,
      programId,
      url: `/program/share/${linkId}`,
      createdAt: new Date(),
      viewCount: 0
    };

    console.log('Created new link:', newLink);

    // Important: Use a callback to ensure we're working with the latest state
    setProgramShareLinks(prevLinks => {
      const updatedLinks = [...prevLinks, newLink];
      return updatedLinks;
    });

    return newLink;
  };

  const getProgramShareLinkByProgramId = (programId: string) => {
    const link = programShareLinks.find(link => link.programId === programId);
    console.log(`Looking for link with programId ${programId}:`, link || 'Not found');
    return link;
  };

  const getProgramShareLinkById = (id: string) => {
    console.log('Current programShareLinks:', programShareLinks);
    const link = programShareLinks.find(link => link.id === id);
    console.log(`Looking for link with id ${id}:`, link || 'Not found');
    return link;
  };

  const incrementProgramShareViewCount = (linkId: string) => {
    console.log(`Incrementing view count for link with id ${linkId}`);

    // Vérifier si le lien existe avant d'incrémenter
    const existingLink = programShareLinks.find(link => link.id === linkId);
    if (!existingLink) {
      console.warn(`Cannot increment view count: link with id ${linkId} not found`);
      return;
    }

    setProgramShareLinks(links => {
      const updatedLinks = links.map(link => {
        if (link.id === linkId) {
          console.log(`Incrementing view count from ${link.viewCount} to ${link.viewCount + 1}`);
          return {
            ...link,
            viewCount: link.viewCount + 1
          };
        }
        return link;
      });

      // No need to save to localStorage anymore

      return updatedLinks;
    });
  };

  // No localStorage effects needed anymore

  const createRegistrationLink = () => {
    const linkId = uuidv4();
    const newLink: RegistrationLink = {
      id: uuidv4(),
      url: `/register/${linkId}`,
      currentRegistrations: 0,
      createdAt: new Date()
    };
    setRegistrationLinks([...registrationLinks, newLink]);
    return newLink;
  };

  const deleteRegistrationLink = (id: string) => {
    console.log("Deleting registration link with ID:", id);
    setRegistrationLinks(links => links.filter(link => link.id !== id));
  };

  const incrementRegistrationCount = (linkIdentifier: string) => {
    console.log("Incrementing registration count for link ID:", linkIdentifier);

    setRegistrationLinks(links => {
      const updatedLinks = links.map(link => {
        // Extract the last segment of the URL to compare with linkIdentifier
        const urlSegments = link.url.split('/');
        const lastSegment = urlSegments[urlSegments.length - 1];

        if (lastSegment === linkIdentifier) {
          console.log(`Found matching link: ${link.url}, incrementing count from ${link.currentRegistrations} to ${link.currentRegistrations + 1}`);
          return {
            ...link,
            currentRegistrations: link.currentRegistrations + 1
          };
        }
        return link;
      });

      return updatedLinks;
    });
  };

  const getRegionById = (id: string) => regions.find(region => region.id === id);
  const getDistrictById = (id: string) => districts.find(district => district.id === id);
  const getAssemblyById = (id: string) => assemblies.find(assembly => assembly.id === id);
  const getYouthById = (id: string) => youths.find(youth => youth.id === id);

  const getDistrictsByRegion = (regionId: string) => districts.filter(district => district.regionId === regionId);
  const getAssembliesByDistrict = (districtId: string) => assemblies.filter(assembly => assembly.districtId === districtId);
  const getYouthsByAssembly = (assemblyId: string) => youths.filter(youth => youth.assemblyId === assemblyId);

  const getYouthsByDistrict = (districtId: string) => {
    const districtAssemblies = assemblies.filter(assembly => assembly.districtId === districtId);
    const assemblyIds = districtAssemblies.map(assembly => assembly.id);
    return youths.filter(youth => assemblyIds.includes(youth.assemblyId));
  };

  const getYouthsByRegion = (regionId: string) => {
    const regionDistricts = districts.filter(district => district.regionId === regionId);
    const districtIds = regionDistricts.map(district => district.id);
    const regionAssemblies = assemblies.filter(assembly => districtIds.includes(assembly.districtId));
    const assemblyIds = regionAssemblies.map(assembly => assembly.id);
    return youths.filter(youth => assemblyIds.includes(youth.assemblyId));
  };

  const updateYouth = (id: string, updates: Partial<Youth>) => {
    setYouths(youths.map(youth => {
      if (youth.id === id) {
        return { ...youth, ...updates };
      }
      return youth;
    }));
  };

  const approvePermission = (id: string, approverId: string) => {
    setPermissions(permissions.map(permission => {
      if (permission.id === id) {
        return { ...permission, approved: true, approvedBy: approverId };
      }
      return permission;
    }));
  };

  // Function to clear stored data (for debugging purposes)
  const clearStoredData = () => {
    // Reset all state to initial values
    setYouths([]);
    setPayments([]);
    setPermissions([]);
    setSermons([]);
    setDailyPrograms([]);
    setProgramShareLinks([]);
    setAccessRights([]);
    setMagicLinks([]);
    setAccessLogs([]);
    setRegistrationLinks([
      {
        id: 'test-link-id',
        url: '/register/test-link',
        currentRegistrations: 0,
        createdAt: new Date()
      },
      {
        id: 'specific-link-id',
        url: '/register/15baf29b-a909-4eff-a8bb-86378f1fbcc2',
        currentRegistrations: 0,
        createdAt: new Date()
      }
    ]);
    console.log('Cleared all stored data');
  };

  // Fonctions pour les droits d'accès
  const createAccessRight = (accessRight: Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>) => {
    const now = new Date();
    const newAccessRight: AccessRight = {
      id: uuidv4(),
      ...accessRight,
      createdAt: now,
      updatedAt: now
    };

    setAccessRights(prevRights => [...prevRights, newAccessRight]);
    return newAccessRight;
  };

  const updateAccessRight = (id: string, updates: Partial<Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>>) => {
    setAccessRights(prevRights =>
      prevRights.map(right => {
        if (right.id === id) {
          return {
            ...right,
            ...updates,
            updatedAt: new Date()
          };
        }
        return right;
      })
    );
  };

  const deleteAccessRight = (id: string) => {
    // Vérifier si des liens magiques utilisent ce droit d'accès
    const linkedMagicLinks = magicLinks.filter(link => link.accessRightId === id);
    if (linkedMagicLinks.length > 0) {
      // Désactiver tous les liens magiques associés
      linkedMagicLinks.forEach(link => {
        revokeMagicLink(link.id);
      });
    }

    // Supprimer le droit d'accès
    setAccessRights(prevRights => prevRights.filter(right => right.id !== id));
  };

  const getAccessRightById = (id: string) => accessRights.find(right => right.id === id);

  // Fonctions pour les liens magiques
  const createMagicLink = (accessRightId: string, name: string, expiresAt?: Date) => {
    const linkId = uuidv4();
    const newLink: MagicLink = {
      id: linkId,
      accessRightId,
      name,
      url: `/access/${linkId}`,
      createdAt: new Date(),
      expiresAt,
      viewCount: 0,
      editCount: 0,
      active: true
    };

    setMagicLinks(prevLinks => [...prevLinks, newLink]);
    return newLink;
  };

  const updateMagicLink = (id: string, updates: Partial<Omit<MagicLink, 'id' | 'createdAt'>>) => {
    setMagicLinks(prevLinks =>
      prevLinks.map(link => {
        if (link.id === id) {
          return {
            ...link,
            ...updates
          };
        }
        return link;
      })
    );
  };

  const revokeMagicLink = (id: string) => {
    updateMagicLink(id, { active: false });
  };

  const deleteMagicLink = (id: string) => {
    // Supprimer les logs d'accès associés à ce lien
    setAccessLogs(prevLogs => prevLogs.filter(log => log.magicLinkId !== id));

    // Supprimer le lien magique
    setMagicLinks(prevLinks => prevLinks.filter(link => link.id !== id));
  };

  const getMagicLinkById = (id: string) => magicLinks.find(link => link.id === id);

  const getMagicLinksByAccessRightId = (accessRightId: string) =>
    magicLinks.filter(link => link.accessRightId === accessRightId);

  const incrementMagicLinkViewCount = (id: string) => {
    setMagicLinks(prevLinks =>
      prevLinks.map(link => {
        if (link.id === id) {
          return {
            ...link,
            viewCount: link.viewCount + 1,
            lastUsedAt: new Date()
          };
        }
        return link;
      })
    );
  };

  const incrementMagicLinkEditCount = (id: string) => {
    setMagicLinks(prevLinks =>
      prevLinks.map(link => {
        if (link.id === id) {
          return {
            ...link,
            editCount: link.editCount + 1,
            lastUsedAt: new Date()
          };
        }
        return link;
      })
    );
  };

  // Fonctions pour les logs d'accès
  const addAccessLog = (log: Omit<AccessLog, 'id' | 'timestamp'>) => {
    const newLog: AccessLog = {
      id: uuidv4(),
      ...log,
      timestamp: new Date()
    };

    setAccessLogs(prevLogs => [...prevLogs, newLog]);

    // Mettre à jour les compteurs du lien magique
    if (log.action === 'view') {
      incrementMagicLinkViewCount(log.magicLinkId);
    } else if (log.action === 'edit') {
      incrementMagicLinkEditCount(log.magicLinkId);
    }
  };

  const getAccessLogsByMagicLinkId = (magicLinkId: string) =>
    accessLogs.filter(log => log.magicLinkId === magicLinkId);

  // Fonction pour vérifier les droits d'accès
  const checkAccess = (magicLinkId: string, feature: FeatureType, accessType: AccessType): boolean => {
    // Vérifier si le lien existe et est actif
    const magicLink = getMagicLinkById(magicLinkId);
    if (!magicLink || !magicLink.active) {
      return false;
    }

    // Vérifier si le lien n'a pas expiré
    if (magicLink.expiresAt && new Date() > magicLink.expiresAt) {
      return false;
    }

    // Récupérer les droits d'accès associés
    const accessRight = getAccessRightById(magicLink.accessRightId);
    if (!accessRight) {
      return false;
    }

    // Vérifier les droits pour la fonctionnalité demandée
    const featureAccess = accessRight.features.find(f => f.feature === feature);
    if (!featureAccess) {
      return false;
    }

    // Vérifier le type d'accès
    if (featureAccess.access === 'read-write') {
      return true;
    } else if (featureAccess.access === 'read' && accessType === 'read') {
      return true;
    } else if (featureAccess.access === 'write' && accessType === 'write') {
      return true;
    }

    return false;
  };

  return (
    <DataContext.Provider value={{
      regions,
      districts,
      assemblies,
      youths,
      payments,
      permissions,
      sermons,
      dailyPrograms,
      registrationLinks,
      programShareLinks,
      accessRights,
      magicLinks,
      accessLogs,

      addRegion,
      addDistrict,
      addAssembly,
      addYouth,
      addPayment,
      addPermission,
      addSermon,
      addDailyProgram,
      updateDailyProgram,
      updateProgramActivities,
      updateSermon,
      createRegistrationLink,
      deleteRegistrationLink,
      incrementRegistrationCount,
      createProgramShareLink,
      getProgramShareLinkByProgramId,
      incrementProgramShareViewCount,

      // Fonctions pour les droits d'accès
      createAccessRight,
      updateAccessRight,
      deleteAccessRight,
      getAccessRightById,

      // Fonctions pour les liens magiques
      createMagicLink,
      updateMagicLink,
      revokeMagicLink,
      deleteMagicLink,
      getMagicLinkById,
      getMagicLinksByAccessRightId,
      incrementMagicLinkViewCount,
      incrementMagicLinkEditCount,

      // Fonctions pour les logs d'accès
      addAccessLog,
      getAccessLogsByMagicLinkId,

      // Fonction pour vérifier les droits d'accès
      checkAccess,

      getRegionById,
      getDistrictById,
      getAssemblyById,
      getYouthById,
      getDailyProgramById,
      getSermonById,
      getProgramShareLinkById,

      getDistrictsByRegion,
      getAssembliesByDistrict,
      getYouthsByAssembly,
      getYouthsByDistrict,
      getYouthsByRegion,

      updateYouth,
      approvePermission,
      clearStoredData
    }}>
      {children}
    </DataContext.Provider>
  );
};

export const useData = () => {
  const context = useContext(DataContext);
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};
