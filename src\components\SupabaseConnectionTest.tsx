import { supabase } from '@/lib/supabase';
import { AlertCircle, CheckCircle, RefreshCw } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Skeleton } from './ui/skeleton';

const SupabaseConnectionTest = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [youthCount, setYouthCount] = useState<number | null>(null);
  const [rawData, setRawData] = useState<any[] | null>(null);

  const testConnection = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Test basic connection with count
      const { count, error: countError } = await supabase
        .from('youths')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        throw countError;
      }

      console.log('Count result:', count);

      // Get actual youth data
      const { data: youthData, error: youthError } = await supabase
        .from('youths')
        .select('*')
        .limit(5);

      if (youthError) {
        throw youthError;
      }

      console.log('Youth data:', youthData);

      // Set the count and raw data
      setYouthCount(count || 0);
      setRawData(youthData || []);
    } catch (err) {
      console.error('Error testing Supabase connection:', err);
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Supabase Connection Test
          {isLoading ? (
            <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />
          ) : error ? (
            <AlertCircle className="h-5 w-5 text-red-500" />
          ) : (
            <CheckCircle className="h-5 w-5 text-green-500" />
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ) : error ? (
          <div className="text-red-500">
            <p className="font-medium">Error connecting to Supabase:</p>
            <pre className="mt-2 p-2 bg-red-50 rounded text-xs overflow-auto max-h-60">
              {error.message}
            </pre>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="font-medium">Connection successful!</p>
            <p>Youth count: {youthCount}</p>

            <div>
              <p className="font-medium mb-2">Raw youth data (first 5 records):</p>
              <pre className="p-2 bg-gray-100 rounded text-xs overflow-auto max-h-60">
                {JSON.stringify(rawData, null, 2)}
              </pre>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={testConnection}
              className="flex items-center gap-1"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SupabaseConnectionTest;
