import { supabase } from '@/lib/supabase';
import * as services from '@/services';
import { Region, District, Assembly, Youth } from '@/types';

// Mock data for testing
const mockRegion: Omit<Region, 'id'> = {
  name: 'Test Region'
};

const mockDistrict: Omit<District, 'id'> = {
  name: 'Test District',
  regionId: '' // Will be set during test
};

const mockAssembly: Omit<Assembly, 'id'> = {
  name: 'Test Assembly',
  districtId: '' // Will be set during test
};

const mockYouth: Omit<Youth, 'id' | 'payments' | 'registrationDate'> = {
  firstName: 'Test',
  lastName: 'User',
  gender: 'male',
  assemblyId: '' // Will be set during test
};

// Helper function to clean up test data
async function cleanupTestData(ids: { regionId?: string, districtId?: string, assemblyId?: string, youthId?: string }) {
  const { regionId, districtId, assemblyId, youthId } = ids;
  
  // Delete in reverse order to respect foreign key constraints
  if (youthId) {
    await supabase.from('youths').delete().eq('id', youthId);
  }
  
  if (assemblyId) {
    await supabase.from('assemblies').delete().eq('id', assemblyId);
  }
  
  if (districtId) {
    await supabase.from('districts').delete().eq('id', districtId);
  }
  
  if (regionId) {
    await supabase.from('regions').delete().eq('id', regionId);
  }
}

describe('Supabase Services', () => {
  // IDs for created test data
  let testIds: { regionId?: string, districtId?: string, assemblyId?: string, youthId?: string } = {};
  
  // Clean up after all tests
  afterAll(async () => {
    await cleanupTestData(testIds);
  });
  
  describe('Regions Service', () => {
    test('should create a new region', async () => {
      const region = await services.regionsService.create(mockRegion);
      expect(region).toHaveProperty('id');
      expect(region.name).toBe(mockRegion.name);
      
      // Save ID for cleanup
      testIds.regionId = region.id;
    });
    
    test('should get all regions', async () => {
      const regions = await services.regionsService.getAll();
      expect(Array.isArray(regions)).toBe(true);
      expect(regions.length).toBeGreaterThan(0);
      
      // Check if our test region is in the list
      const testRegion = regions.find(r => r.id === testIds.regionId);
      expect(testRegion).toBeDefined();
      expect(testRegion?.name).toBe(mockRegion.name);
    });
    
    test('should get a region by ID', async () => {
      if (!testIds.regionId) {
        throw new Error('Region ID not set');
      }
      
      const region = await services.regionsService.getById(testIds.regionId);
      expect(region).not.toBeNull();
      expect(region?.id).toBe(testIds.regionId);
      expect(region?.name).toBe(mockRegion.name);
    });
    
    test('should update a region', async () => {
      if (!testIds.regionId) {
        throw new Error('Region ID not set');
      }
      
      const updatedName = 'Updated Test Region';
      const updatedRegion = await services.regionsService.update(testIds.regionId, { name: updatedName });
      expect(updatedRegion.id).toBe(testIds.regionId);
      expect(updatedRegion.name).toBe(updatedName);
      
      // Verify the update
      const region = await services.regionsService.getById(testIds.regionId);
      expect(region?.name).toBe(updatedName);
    });
  });
  
  describe('Districts Service', () => {
    test('should create a new district', async () => {
      if (!testIds.regionId) {
        throw new Error('Region ID not set');
      }
      
      const district = await services.districtsService.create({
        ...mockDistrict,
        regionId: testIds.regionId
      });
      
      expect(district).toHaveProperty('id');
      expect(district.name).toBe(mockDistrict.name);
      expect(district.regionId).toBe(testIds.regionId);
      
      // Save ID for cleanup
      testIds.districtId = district.id;
    });
    
    test('should get districts by region', async () => {
      if (!testIds.regionId) {
        throw new Error('Region ID not set');
      }
      
      const districts = await services.districtsService.getByRegion(testIds.regionId);
      expect(Array.isArray(districts)).toBe(true);
      expect(districts.length).toBeGreaterThan(0);
      
      // Check if our test district is in the list
      const testDistrict = districts.find(d => d.id === testIds.districtId);
      expect(testDistrict).toBeDefined();
      expect(testDistrict?.name).toBe(mockDistrict.name);
    });
  });
  
  describe('Assemblies Service', () => {
    test('should create a new assembly', async () => {
      if (!testIds.districtId) {
        throw new Error('District ID not set');
      }
      
      const assembly = await services.assembliesService.create({
        ...mockAssembly,
        districtId: testIds.districtId
      });
      
      expect(assembly).toHaveProperty('id');
      expect(assembly.name).toBe(mockAssembly.name);
      expect(assembly.districtId).toBe(testIds.districtId);
      
      // Save ID for cleanup
      testIds.assemblyId = assembly.id;
    });
    
    test('should get assemblies by district', async () => {
      if (!testIds.districtId) {
        throw new Error('District ID not set');
      }
      
      const assemblies = await services.assembliesService.getByDistrict(testIds.districtId);
      expect(Array.isArray(assemblies)).toBe(true);
      expect(assemblies.length).toBeGreaterThan(0);
      
      // Check if our test assembly is in the list
      const testAssembly = assemblies.find(a => a.id === testIds.assemblyId);
      expect(testAssembly).toBeDefined();
      expect(testAssembly?.name).toBe(mockAssembly.name);
    });
  });
  
  describe('Youths Service', () => {
    test('should create a new youth', async () => {
      if (!testIds.assemblyId) {
        throw new Error('Assembly ID not set');
      }
      
      const youth = await services.youthsService.create({
        ...mockYouth,
        assemblyId: testIds.assemblyId,
        registrationDate: new Date()
      });
      
      expect(youth).toHaveProperty('id');
      expect(youth.firstName).toBe(mockYouth.firstName);
      expect(youth.lastName).toBe(mockYouth.lastName);
      expect(youth.gender).toBe(mockYouth.gender);
      expect(youth.assemblyId).toBe(testIds.assemblyId);
      
      // Save ID for cleanup
      testIds.youthId = youth.id;
    });
    
    test('should get a youth by ID', async () => {
      if (!testIds.youthId) {
        throw new Error('Youth ID not set');
      }
      
      const youth = await services.youthsService.getById(testIds.youthId);
      expect(youth).not.toBeNull();
      expect(youth?.id).toBe(testIds.youthId);
      expect(youth?.firstName).toBe(mockYouth.firstName);
      expect(youth?.lastName).toBe(mockYouth.lastName);
    });
    
    test('should update a youth', async () => {
      if (!testIds.youthId) {
        throw new Error('Youth ID not set');
      }
      
      const updatedFirstName = 'Updated';
      const updatedYouth = await services.youthsService.update(testIds.youthId, { firstName: updatedFirstName });
      expect(updatedYouth.id).toBe(testIds.youthId);
      expect(updatedYouth.firstName).toBe(updatedFirstName);
      
      // Verify the update
      const youth = await services.youthsService.getById(testIds.youthId);
      expect(youth?.firstName).toBe(updatedFirstName);
    });
  });
});
