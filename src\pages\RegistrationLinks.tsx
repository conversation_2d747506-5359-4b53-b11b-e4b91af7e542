import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useToast } from '@/components/ui/use-toast';
import { useData } from '@/contexts/DataContext';
import { format } from 'date-fns';
import { Calendar, Copy, Link as LinkIcon, Trash } from 'lucide-react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';

const RegistrationLinks = () => {
  const { toast } = useToast();
  const { registrationLinks, createRegistrationLink, deleteRegistrationLink } = useData();
  const [selectedLinkId, setSelectedLinkId] = useState<string | null>(null);
  const { t } = useTranslation();

  const handleCreateLink = () => {
    const link = createRegistrationLink();
    toast({
      title: t('common.success'),
      description: t('programs.linkCreated')
    });
    setSelectedLinkId(link.id);
  };

  const copyToClipboard = (text: string) => {
    // Create a temporary input element
    const tempInput = document.createElement('input');
    tempInput.value = text;
    document.body.appendChild(tempInput);

    // Select and copy the text
    tempInput.select();
    tempInput.setSelectionRange(0, 99999); // For mobile devices

    try {
      // Execute copy command
      const successful = document.execCommand('copy');

      if (successful) {
        toast({
          title: t('common.copied'),
          description: t('accessRights.linkCopied')
        });
      } else {
        // Fallback to the clipboard API if execCommand fails
        navigator.clipboard.writeText(text).then(
          () => {
            toast({
              title: t('common.copied'),
              description: t('accessRights.linkCopied')
            });
          },
          (err) => {
            toast({
              title: t('common.error'),
              description: t('accessRights.linkCopyError'),
              variant: "destructive"
            });
            console.error('Error copying link:', err);
          }
        );
      }
    } catch (err) {
      toast({
        title: t('common.error'),
        description: t('accessRights.linkCopyError'),
        variant: "destructive"
      });
      console.error('Error copying link:', err);
    } finally {
      // Remove the temporary input element
      document.body.removeChild(tempInput);
    }
  };

  // Generate the full registration link URL
  const getFullLink = (linkUrl: string) => {
    // Extract just the linkId from the url path
    const linkId = linkUrl.split('/').pop();
    // Construct the absolute URL with the correct route
    const fullUrl = `${window.location.origin}/register/${linkId}`;
    console.log(`Generated full URL: ${fullUrl} from link URL: ${linkUrl}`);
    return fullUrl;
  };

  const handleDeleteLink = (id: string) => {
    // Protect test links from deletion
    if (id === 'test-link-id' || id === 'specific-link-id') {
      toast({
        title: t('common.error'),
        description: t('programs.testLinkDeleteError'),
        variant: "destructive"
      });
      return;
    }

    // Ask for confirmation before deleting
    if (window.confirm(t('programs.confirmDeleteLink'))) {
      // Check if this is the selected link
      if (id === selectedLinkId) {
        setSelectedLinkId(null);
      }

      // Delete the link
      deleteRegistrationLink(id);

      toast({
        title: t('common.success'),
        description: t('programs.linkDeleted')
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('sidebar.registrationLinks')}</h1>
          <p className="text-gray-500">{t('programs.manageRegistrationLinks')}</p>
        </div>
        <Button onClick={handleCreateLink}>
          <LinkIcon className="h-4 w-4 mr-2" />
          {t('programs.createLink')}
        </Button>
      </div>

      {selectedLinkId && (
        <Card className="mb-6 border-2 border-primary/20 bg-primary/5">
          <CardHeader>
            <CardTitle className="text-lg">{t('programs.newLinkCreated')}</CardTitle>
            <CardDescription>
              {t('programs.shareLinkWithYouths')}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Input
                value={getFullLink(registrationLinks.find(l => l.id === selectedLinkId)?.url || '')}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(getFullLink(registrationLinks.find(l => l.id === selectedLinkId)?.url || ''))}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>{t('programs.activeLinks')}</CardTitle>
          <CardDescription>
            {t('programs.registrationLinksList')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {registrationLinks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <LinkIcon className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="mb-1">{t('programs.noRegistrationLinks')}</p>
              <p className="text-sm">{t('programs.clickCreateLinkHint')}</p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>{t('common.link')}</TableHead>
                  <TableHead>{t('accessRights.creationDate')}</TableHead>
                  <TableHead>{t('programs.registrations')}</TableHead>
                  <TableHead className="w-20">{t('common.actions')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {registrationLinks.map((link) => (
                  <TableRow key={link.id}>
                    <TableCell className="font-mono text-sm truncate max-w-[200px]">
                      {getFullLink(link.url)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Calendar className="h-3.5 w-3.5 mr-2 text-gray-500" />
                        {format(new Date(link.createdAt), 'dd/MM/yyyy')}
                      </div>
                    </TableCell>
                    <TableCell>{link.currentRegistrations}</TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => copyToClipboard(getFullLink(link.url))}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteLink(link.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default RegistrationLinks;
