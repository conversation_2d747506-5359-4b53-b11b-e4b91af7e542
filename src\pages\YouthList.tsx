import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/components/ui/use-toast";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { format } from "date-fns";
import { AlertCircle, ChevronLeft, ChevronRight, Loader2, Plus, Printer, RefreshCw, Search } from "lucide-react";
import React, { useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";


// Add print styles
const printStyles = `
  @media print {
    body * {
      visibility: hidden;
    }
    .print-section, .print-section * {
      visibility: visible !important;
      display: block !important;
    }
    .print-section {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }
    .no-print {
      display: none !important;
    }
  }
`;

const YouthList = () => {
  const {
    youths,
    regions,
    districts,
    assemblies,
    isLoading,
    errors,
    getRegionById,
    getDistrictById,
    getAssemblyById,
    getDistrictsByRegion,
    getAssembliesByDistrict,
    refreshData
  } = useSupabaseData();

  const navigate = useNavigate();
  const { t } = useTranslation();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedDistrict, setSelectedDistrict] = useState("all");
  const [selectedAssembly, setSelectedAssembly] = useState("all");
  const [filteredDistricts, setFilteredDistricts] = useState(districts);
  const [filteredAssemblies, setFilteredAssemblies] = useState(assemblies);
  const printSectionRef = useRef<HTMLDivElement>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page for table view

  // Handle region change
  const handleRegionChange = (value: string) => {
    setSelectedRegion(value);
    setSelectedDistrict("all");
    setSelectedAssembly("all");

    if (value && value !== "all") {
      const districtsInRegion = getDistrictsByRegion(value);
      setFilteredDistricts(districtsInRegion);
      setFilteredAssemblies([]);
    } else {
      setFilteredDistricts(districts);
      setFilteredAssemblies([]);
    }
  };

  // Handle district change
  const handleDistrictChange = (value: string) => {
    setSelectedDistrict(value);
    setSelectedAssembly("all");

    if (value && value !== "all") {
      const assembliesInDistrict = getAssembliesByDistrict(value);
      setFilteredAssemblies(assembliesInDistrict);
    } else {
      setFilteredAssemblies([]);
    }
  };

  // Handle assembly change
  const handleAssemblyChange = (value: string) => {
    setSelectedAssembly(value);
  };

  // Filter youths based on search term and selected filters
  const filteredYouths = youths.filter(youth => {
    const matchesSearch =
      searchTerm === "" ||
      youth.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      youth.lastName.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesAssembly = selectedAssembly === "all" || youth.assemblyId === selectedAssembly;

    if (selectedAssembly !== "all") return matchesSearch && matchesAssembly;

    const matchesDistrict = selectedDistrict === "all" ||
      getAssembliesByDistrict(selectedDistrict).some(a => a.id === youth.assemblyId);

    if (selectedDistrict !== "all") return matchesSearch && matchesDistrict;

    const matchesRegion = selectedRegion === "all" ||
      getDistrictsByRegion(selectedRegion).some(d =>
        getAssembliesByDistrict(d.id).some(a => a.id === youth.assemblyId)
      );

    return matchesSearch && matchesRegion;
  });

  // Calculate pagination
  const totalPages = Math.ceil(filteredYouths.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredYouths.slice(indexOfFirstItem, indexOfLastItem);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchTerm, selectedRegion, selectedDistrict, selectedAssembly]);

  const handleRefreshData = async () => {
    try {
      await refreshData();
      toast({
        title: t('common.success'),
        description: t('common.dataRefreshed'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('common.errorRefreshingData'),
        variant: 'destructive',
      });
    }
  };

  // Handle printing
  const handlePrint = () => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      toast({
        title: t('common.error'),
        description: "Could not open print window. Please check your popup settings.",
        variant: 'destructive',
      });
      return;
    }

    // Generate the HTML content for printing
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${t('youths.title')}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; margin-bottom: 20px; }
            table { width: 100%; border-collapse: collapse; }
            th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f2f2f2; }
            .header { text-align: center; margin-bottom: 30px; }
            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${t('youths.title')}</h1>
            ${selectedRegion !== "all" ? `<p>${t('common.region')}: ${getRegionById(selectedRegion)?.name}</p>` : ''}
            ${selectedDistrict !== "all" ? `<p>${t('common.district')}: ${getDistrictById(selectedDistrict)?.name}</p>` : ''}
            ${selectedAssembly !== "all" ? `<p>${t('common.assembly')}: ${getAssemblyById(selectedAssembly)?.name}</p>` : ''}
            <p>${t('common.printDate')}: ${format(new Date(), 'dd/MM/yyyy')}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>${t('common.name')}</th>
                <th>${t('common.gender')}</th>
                <th>${t('common.region')}</th>
                <th>${t('common.district')}</th>
                <th>${t('common.assembly')}</th>
                <th>${t('common.registrationDate')}</th>
              </tr>
            </thead>
            <tbody>
              ${filteredYouths.map(youth => {
      const assembly = getAssemblyById(youth.assemblyId);
      const district = assembly ? getDistrictById(assembly.districtId) : null;
      const region = district ? getRegionById(district.regionId) : null;

      return `
                  <tr>
                    <td>${youth.firstName} ${youth.lastName}</td>
                    <td>${youth.gender === 'male' ? t('common.male') : t('common.female')}</td>
                    <td>${region?.name || t('common.unknown')}</td>
                    <td>${district?.name || t('common.unknown')}</td>
                    <td>${assembly?.name || t('common.unknown')}</td>
                    <td>${format(new Date(youth.registrationDate), 'dd/MM/yyyy')}</td>
                  </tr>
                `;
    }).join('')}
            </tbody>
          </table>

          <div class="footer">
            <p>${t('common.total')}: ${filteredYouths.length} ${t('youths.youths')}</p>
          </div>
        </body>
      </html>
    `;

    // Write the content to the new window
    printWindow.document.write(printContent);
    printWindow.document.close();

    // Wait for the content to load before printing
    printWindow.onload = function () {
      printWindow.print();
      // Close the window after printing (optional)
      // printWindow.close();
    };

    toast({
      title: t('common.success'),
      description: t('common.printSuccess'),
    });
  };

  // Check if any data is loading
  const isDataLoading = isLoading.youths || isLoading.regions ||
    isLoading.districts || isLoading.assemblies;

  // Check if there are any errors
  const hasErrors = errors.youths || errors.regions ||
    errors.districts || errors.assemblies;

  if (hasErrors) {
    return (
      <div className="p-6 bg-red-50 rounded-lg border border-red-200">
        <h2 className="text-xl font-bold text-red-700 mb-4">{t('common.error')}</h2>
        <p className="mb-4 text-red-600">{t('common.errorLoadingData')}</p>
        <div className="bg-white p-4 rounded-md shadow mb-4 overflow-auto max-h-60">
          <pre className="text-xs text-red-500">
            {JSON.stringify({
              youths: errors.youths?.message,
              regions: errors.regions?.message,
              districts: errors.districts?.message,
              assemblies: errors.assemblies?.message
            }, null, 2)}
          </pre>
        </div>
        <Button onClick={handleRefreshData} className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          {t('common.tryAgain')}
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <style>{printStyles}</style>
      {/* Printable component - hidden from normal view */}
      <div ref={printSectionRef} className="hidden print-section">
        <div className="p-8">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold">{t('youths.title')}</h1>
            {selectedRegion !== "all" && (
              <p className="text-lg">
                {t('common.region')}: {getRegionById(selectedRegion)?.name}
              </p>
            )}
            {selectedDistrict !== "all" && (
              <p className="text-lg">
                {t('common.district')}: {getDistrictById(selectedDistrict)?.name}
              </p>
            )}
            {selectedAssembly !== "all" && (
              <p className="text-lg">
                {t('common.assembly')}: {getAssemblyById(selectedAssembly)?.name}
              </p>
            )}
            <p className="text-sm text-gray-500 mt-2">
              {t('common.printDate')}: {format(new Date(), 'dd/MM/yyyy')}
            </p>
          </div>

          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b-2 border-gray-200">
                <th className="text-left py-2">{t('common.name')}</th>
                <th className="text-left py-2">{t('common.gender')}</th>
                <th className="text-left py-2">{t('common.region')}</th>
                <th className="text-left py-2">{t('common.district')}</th>
                <th className="text-left py-2">{t('common.assembly')}</th>
                <th className="text-left py-2">{t('common.registrationDate')}</th>
              </tr>
            </thead>
            <tbody>
              {filteredYouths.map(youth => {
                const assembly = getAssemblyById(youth.assemblyId);
                const district = assembly ? getDistrictById(assembly.districtId) : null;
                const region = district ? getRegionById(district.regionId) : null;

                return (
                  <tr key={youth.id} className="border-b border-gray-200">
                    <td className="py-2">{youth.firstName} {youth.lastName}</td>
                    <td className="py-2">{youth.gender === 'male' ? t('common.male') : t('common.female')}</td>
                    <td className="py-2">{region?.name || t('common.unknown')}</td>
                    <td className="py-2">{district?.name || t('common.unknown')}</td>
                    <td className="py-2">{assembly?.name || t('common.unknown')}</td>
                    <td className="py-2">{format(new Date(youth.registrationDate), 'dd/MM/yyyy')}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>

          <div className="mt-6 text-center text-sm text-gray-500">
            {t('common.total')}: {filteredYouths.length} {t('youths.youths')}
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center no-print">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('youths.title')}</h1>
          <p className="text-gray-500">{t('youths.subtitle')}</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshData}
            className="flex items-center gap-1"
            disabled={isDataLoading}
          >
            {isDataLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
            {t('common.refresh')}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={handlePrint}
          >
            <Printer className="h-4 w-4" />
            {t('common.print')}
          </Button>
          <Button
            onClick={() => navigate('/add-youth')}
            className="flex items-center gap-1"
          >
            <Plus className="h-4 w-4" />
            {t('youths.addYouth')}
          </Button>
        </div>
      </div>

      <Card className="no-print">
        <CardHeader>
          <CardTitle>{t('youths.filters')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder={t('youths.searchPlaceholder')}
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                disabled={isDataLoading}
              />
            </div>

            {isDataLoading ? (
              <>
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </>
            ) : (
              <>
                <Select value={selectedRegion} onValueChange={handleRegionChange}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('youths.selectRegion')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.all')}</SelectItem>
                    {regions.map(region => (
                      <SelectItem key={region.id} value={region.id}>{region.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedDistrict} onValueChange={handleDistrictChange} disabled={!selectedRegion || selectedRegion === "all"}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('youths.selectDistrict')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.all')}</SelectItem>
                    {filteredDistricts.map(district => (
                      <SelectItem key={district.id} value={district.id}>{district.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedAssembly} onValueChange={handleAssemblyChange} disabled={!selectedDistrict || selectedDistrict === "all"}>
                  <SelectTrigger>
                    <SelectValue placeholder={t('youths.selectAssembly')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{t('common.all')}</SelectItem>
                    {filteredAssemblies.map(assembly => (
                      <SelectItem key={assembly.id} value={assembly.id}>{assembly.name}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </>
            )}
          </div>
        </CardContent>
      </Card>



      {isDataLoading ? (
        <div className="space-y-4 no-print">
          <Skeleton className="h-10 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      ) : filteredYouths.length === 0 ? (
        <div className="bg-gray-50 p-8 rounded-lg text-center no-print">
          <AlertCircle className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">{t('youths.noYouthsFound')}</h3>
          <p className="text-gray-500 mb-4">{t('youths.tryDifferentFilters')}</p>
          <Button variant="outline" onClick={() => {
            setSearchTerm("");
            setSelectedRegion("all");
            setSelectedDistrict("all");
            setSelectedAssembly("all");
          }}>
            {t('common.clearFilters')}
          </Button>
        </div>
      ) : (
        <div className="no-print">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.name')}</TableHead>
                    <TableHead>{t('common.gender')}</TableHead>
                    <TableHead>{t('common.region')}</TableHead>
                    <TableHead>{t('common.district')}</TableHead>
                    <TableHead>{t('common.assembly')}</TableHead>
                    <TableHead>{t('common.registrationDate')}</TableHead>
                    <TableHead>{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {currentItems.map(youth => {
                    const assembly = getAssemblyById(youth.assemblyId);
                    const district = assembly ? getDistrictById(assembly.districtId) : null;
                    const region = district ? getRegionById(district.regionId) : null;

                    return (
                      <TableRow key={youth.id}>
                        <TableCell className="font-medium">
                          {youth.firstName} {youth.lastName}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${youth.gender === 'male' ? 'bg-blue-500' : 'bg-pink-500'}`}></div>
                            {youth.gender === 'male' ? t('common.male') : t('common.female')}
                          </div>
                        </TableCell>
                        <TableCell>{region?.name || t('common.unknown')}</TableCell>
                        <TableCell>{district?.name || t('common.unknown')}</TableCell>
                        <TableCell>{assembly?.name || t('common.unknown')}</TableCell>
                        <TableCell>{format(new Date(youth.registrationDate), 'dd/MM/yyyy')}</TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => navigate(`/youths/${youth.id}`)}
                            className="w-full"
                          >
                            {t('common.details') || "Détails"}
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-6">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  {t('common.showing')} {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, filteredYouths.length)} {t('common.of')} {filteredYouths.length} {t('youths.youths')}
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    {t('common.previous')}
                  </Button>

                  <div className="flex items-center mx-2">
                    <span className="text-sm font-medium">
                      {currentPage} / {totalPages}
                    </span>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                  >
                    {t('common.next')}
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default YouthList;