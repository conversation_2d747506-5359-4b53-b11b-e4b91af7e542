
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useLanguage } from "@/contexts/LanguageContext";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { enUS, fr } from "date-fns/locale";
import { AlignmentType, Document, HeadingLevel, Packer, Paragraph, TextRun } from "docx";
import { saveAs } from "file-saver";
import { Calendar, Clock, Download, Edit, ExternalLink, MoreHorizontal, Plus, PlusCircle, RefreshCw, Share2, X } from "lucide-react";
import { useRef, useState } from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { useTranslation } from "react-i18next";
import { v4 as uuidv4 } from "uuid";

const Programs = () => {
  const {
    dailyPrograms,
    addDailyProgram,
    updateDailyProgram,
    updateProgramActivities,
    getDailyProgramById,
    createProgramShareLink,
    getProgramShareLinkByProgramId,
    isLoading,
    refreshData
  } = useSupabaseData();
  const { t } = useTranslation();
  const { language } = useLanguage();
  const dateLocale = language === 'fr' ? fr : enUS;
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [programDate, setProgramDate] = useState("");
  const [activities, setActivities] = useState([
    {
      id: uuidv4(),
      title: "",
      description: "",
      startTime: "",
      endTime: "",
      responsiblePerson: ""
    }
  ]);
  const [editMode, setEditMode] = useState(false);
  const [currentProgramId, setCurrentProgramId] = useState<string | null>(null);
  const programCardRef = useRef<HTMLDivElement>(null);

  // Sort programs by date
  const sortedPrograms = [...dailyPrograms].sort((a, b) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  const handleAddActivity = () => {
    setActivities([
      ...activities,
      {
        id: uuidv4(),
        title: "",
        description: "",
        startTime: "",
        endTime: "",
        responsiblePerson: ""
      }
    ]);
  };

  const handleRemoveActivity = (id: string) => {
    if (activities.length === 1) {
      toast({
        title: t('common.error'),
        description: t('programs.atLeastOneActivity'),
        variant: "destructive",
      });
      return;
    }
    setActivities(activities.filter(activity => activity.id !== id));
  };

  const handleActivityChange = (id: string, field: string, value: string) => {
    setActivities(activities.map(activity => {
      if (activity.id === id) {
        return { ...activity, [field]: value };
      }
      return activity;
    }));
  };

  const handleEditProgram = (programId: string) => {
    const program = getDailyProgramById(programId);
    if (program) {
      setCurrentProgramId(programId);
      setProgramDate(format(new Date(program.date), "yyyy-MM-dd"));
      setActivities(program.activities);
      setEditMode(true);
      setIsDialogOpen(true);
    }
  };

  const resetForm = () => {
    setProgramDate("");
    setActivities([{
      id: uuidv4(),
      title: "",
      description: "",
      startTime: "",
      endTime: "",
      responsiblePerson: ""
    }]);
    setEditMode(false);
    setCurrentProgramId(null);
  };

  const handleOpenDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const handleSaveProgram = () => {
    if (!programDate) {
      toast({
        title: t('common.error'),
        description: t('programs.dateRequired'),
        variant: "destructive",
      });
      return;
    }

    const invalidActivities = activities.filter(
      activity => !activity.title || !activity.startTime || !activity.endTime
    );

    if (invalidActivities.length > 0) {
      toast({
        title: t('common.error'),
        description: t('programs.fillAllFields'),
        variant: "destructive",
      });
      return;
    }

    try {
      if (editMode && currentProgramId) {
        updateDailyProgram(currentProgramId, {
          date: new Date(programDate),
          activities
        });
        toast({
          title: t('common.success'),
          description: t('programs.programUpdateSuccess'),
        });
      } else {
        addDailyProgram({
          date: new Date(programDate),
          activities
        });
        toast({
          title: t('common.success'),
          description: t('programs.programAddSuccess'),
        });
      }

      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('programs.operationError'),
        variant: "destructive",
      });
    }
  };

  const handleDownloadWord = async (programId: string) => {
    const program = getDailyProgramById(programId);
    if (!program) return;

    try {
      toast({
        title: t('sermons.preparingDownload'),
        description: t('sermons.generatingDocument'),
      });

      // Create a new document
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                text: `${t('programs.programFor')} ${format(new Date(program.date), "EEEE dd MMMM yyyy", { locale: dateLocale })}`,
                heading: HeadingLevel.HEADING_1,
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({ text: "" }), // Empty paragraph for spacing
              ...program.activities
                .sort((a, b) => {
                  if (a.startTime < b.startTime) return -1;
                  if (a.startTime > b.startTime) return 1;
                  return 0;
                })
                .flatMap((activity) => [
                  new Paragraph({
                    children: [
                      new TextRun({
                        text: `${activity.startTime} - ${activity.endTime}: `,
                        bold: true,
                      }),
                      new TextRun({
                        text: activity.title,
                        bold: true,
                      }),
                    ],
                  }),
                  activity.responsiblePerson
                    ? new Paragraph({
                      text: `${t('programs.responsible')}: ${activity.responsiblePerson}`,
                      indent: { left: 720 }, // 0.5 inch in twips
                    })
                    : new Paragraph({ text: "" }),
                  activity.description
                    ? new Paragraph({
                      text: activity.description,
                      indent: { left: 720 }, // 0.5 inch in twips
                    })
                    : new Paragraph({ text: "" }),
                  new Paragraph({ text: "" }), // Empty paragraph for spacing
                ]),
            ],
          },
        ],
      });

      // Generate and save the document
      const buffer = await Packer.toBlob(doc);
      saveAs(buffer, `programme_${format(new Date(program.date), "yyyy-MM-dd")}.docx`);

      toast({
        title: t('common.success'),
        description: t('sermons.downloadSuccess'),
      });
    } catch (error) {
      console.error("Erreur lors de la génération du document Word:", error);
      toast({
        title: t('common.error'),
        description: t('sermons.downloadError'),
        variant: "destructive",
      });
    }
  };

  const handleShareProgram = (programId: string) => {
    console.log("Sharing program with ID:", programId);
    const program = getDailyProgramById(programId);
    if (!program) {
      console.error("Program not found with ID:", programId);
      toast({
        title: t('common.error'),
        description: t('common.noData'),
        variant: "destructive",
      });
      return;
    }

    try {
      // Check if a share link already exists
      let shareLink = getProgramShareLinkByProgramId(programId);
      console.log("Existing share link:", shareLink);

      // If not, create one
      if (!shareLink) {
        console.log("Creating new share link for program:", programId);
        shareLink = createProgramShareLink(programId);
        console.log("New share link created:", shareLink);
      }

      // Generate the full URL
      const shareUrl = `${window.location.origin}/program/share/${shareLink.id}`;
      console.log("Share URL:", shareUrl);

      // Vérifier que le lien est bien enregistré dans localStorage
      const savedLinks = localStorage.getItem('programShareLinks');
      if (savedLinks) {
        const parsed = JSON.parse(savedLinks);
        const linkExists = parsed.some((link: any) => link.id === shareLink.id);
        console.log("Link exists in localStorage:", linkExists);

        if (!linkExists) {
          console.warn("Link not found in localStorage, forcing save");
          const updatedLinks = [...parsed, shareLink];
          localStorage.setItem('programShareLinks', JSON.stringify(updatedLinks));
        }
      } else {
        console.warn("No links in localStorage, forcing save");
        localStorage.setItem('programShareLinks', JSON.stringify([shareLink]));
      }

      // Copy to clipboard
      navigator.clipboard.writeText(shareUrl)
        .then(() => {
          toast({
            title: t('programs.linkCopied'),
            description: t('programs.linkCopied'),
          });
        })
        .catch(() => {
          toast({
            title: t('common.error'),
            description: t('programs.shareError'),
            variant: "destructive",
          });
        });

      // Try to use the Web Share API if available
      if (navigator.share) {
        navigator.share({
          title: `${t('programs.programFor')} ${format(new Date(program.date), "EEEE dd MMMM yyyy", { locale: dateLocale })}`,
          text: t('programs.todayProgram'),
          url: shareUrl
        }).catch((error) => {
          console.error("Erreur lors du partage:", error);
        });
      }

      // Afficher l'URL pour faciliter le débogage
      toast({
        title: "Lien de partage",
        description: shareUrl,
      });
    } catch (error) {
      console.error("Erreur lors de la création du lien de partage:", error);
      toast({
        title: t('common.error'),
        description: t('programs.shareError'),
        variant: "destructive",
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('programs.title')}</h1>
          <p className="text-gray-500">{t('programs.subtitle')}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.programs}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.programs ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
          <Button onClick={handleOpenDialog}>
            <PlusCircle className="h-4 w-4 mr-2" />
            {t('programs.newProgram')}
          </Button>
        </div>
      </div>

      {isLoading.programs ? (
        <Card>
          <CardContent className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          </CardContent>
        </Card>
      ) : sortedPrograms.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Calendar className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium mb-2">{t('programs.noPrograms')}</h3>
            <p className="text-gray-500 mb-6">{t('programs.noProgramsSubtitle')}</p>
            <Button onClick={handleOpenDialog}>
              <PlusCircle className="h-4 w-4 mr-2" />
              {t('programs.createProgram')}
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue={sortedPrograms[0].id}>
          <TabsList className="mb-4 w-full flex overflow-x-auto">
            {sortedPrograms.map((program) => (
              <TabsTrigger key={program.id} value={program.id}>
                <Calendar className="h-4 w-4 mr-2" />
                {format(new Date(program.date), "EEEE dd MMMM", { locale: dateLocale })}
              </TabsTrigger>
            ))}
          </TabsList>

          {sortedPrograms.map((program) => (
            <TabsContent key={program.id} value={program.id}>
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <CardTitle>
                      {t('programs.programFor')} {format(new Date(program.date), "EEEE dd MMMM yyyy", { locale: dateLocale })}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditProgram(program.id)}>
                            <Edit className="h-4 w-4 mr-2" />
                            {t('programs.modify')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDownloadWord(program.id)}>
                            <Download className="h-4 w-4 mr-2" />
                            {t('programs.download')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleShareProgram(program.id)}>
                            <Share2 className="h-4 w-4 mr-2" />
                            {t('programs.share')}
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            const shareLink = getProgramShareLinkByProgramId(program.id);
                            if (shareLink) {
                              window.open(`/program/share/${shareLink.id}`, '_blank');
                            } else {
                              toast({
                                title: t('common.error'),
                                description: t('programs.noShareLink'),
                                variant: "destructive",
                              });
                            }
                          }}>
                            <ExternalLink className="h-4 w-4 mr-2" />
                            {t('programs.testLink')}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div id={`program-${program.id}`} ref={program.id === currentProgramId ? programCardRef : null} className="space-y-6">
                    <DragDropContext
                      onDragEnd={(result) => {
                        if (!result.destination) return;

                        const items = Array.from(program.activities);
                        const [reorderedItem] = items.splice(result.source.index, 1);
                        items.splice(result.destination.index, 0, reorderedItem);

                        // Update the activities order
                        updateProgramActivities(program.id, items);

                        toast({
                          title: t('programs.activitiesReordered'),
                          description: t('programs.activitiesReorderedSuccess'),
                        });
                      }}
                    >
                      <Droppable droppableId={`program-${program.id}-activities`}>
                        {(provided) => (
                          <div
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            className="space-y-6"
                          >
                            {program.activities
                              .map((activity, index) => (
                                <Draggable
                                  key={activity.id}
                                  draggableId={activity.id}
                                  index={index}
                                >
                                  {(provided) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                      className="grid grid-cols-12 gap-4 bg-white rounded-lg p-3 border border-transparent hover:border-gray-200 cursor-move"
                                    >
                                      <div className="col-span-3 md:col-span-2 flex flex-col">
                                        <div className="flex items-center text-primary font-medium">
                                          <Clock className="h-4 w-4 mr-1" />
                                          {activity.startTime}
                                        </div>
                                        <div className="text-xs text-gray-500 ml-5">
                                          {activity.endTime}
                                        </div>
                                      </div>
                                      <div className="col-span-9 md:col-span-10">
                                        <h3 className="font-medium">{activity.title}</h3>
                                        {activity.responsiblePerson && (
                                          <p className="text-sm text-gray-500">
                                            {t('programs.responsible')}: {activity.responsiblePerson}
                                          </p>
                                        )}
                                        {activity.description && (
                                          <div className="max-h-32 overflow-y-auto mt-1">
                                            <p className="text-sm break-words whitespace-pre-wrap">{activity.description}</p>
                                          </div>
                                        )}
                                      </div>
                                      <div className="col-span-12">
                                        <Separator className="mt-4" />
                                      </div>
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                            {provided.placeholder}
                          </div>
                        )}
                      </Droppable>
                    </DragDropContext>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>
      )}

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{editMode ? t('programs.editProgram') : t('programs.newProgram')}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4 flex-1 overflow-y-auto pr-2">
            <div className="space-y-2">
              <Label htmlFor="date">{t('programs.programDate')}</Label>
              <Input
                id="date"
                type="date"
                value={programDate}
                onChange={(e) => setProgramDate(e.target.value)}
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-4">{t('programs.activities')}</h3>

              {activities.map((activity, index) => (
                <div key={activity.id} className="mb-6 p-4 border rounded-lg">
                  <div className="flex justify-between items-center mb-4">
                    <h4 className="font-medium">{t('programs.activityNumber')} {index + 1}</h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveActivity(activity.id)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor={`title-${activity.id}`}>{t('programs.activityTitle')}</Label>
                      <Input
                        id={`title-${activity.id}`}
                        value={activity.title}
                        onChange={(e) => handleActivityChange(activity.id, "title", e.target.value)}
                        placeholder={t('programs.activityTitlePlaceholder')}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor={`startTime-${activity.id}`}>{t('programs.startTime')}*</Label>
                        <Input
                          id={`startTime-${activity.id}`}
                          type="time"
                          value={activity.startTime}
                          onChange={(e) => handleActivityChange(activity.id, "startTime", e.target.value)}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor={`endTime-${activity.id}`}>{t('programs.endTime')}*</Label>
                        <Input
                          id={`endTime-${activity.id}`}
                          type="time"
                          value={activity.endTime}
                          onChange={(e) => handleActivityChange(activity.id, "endTime", e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`responsible-${activity.id}`}>{t('programs.responsible')}</Label>
                      <Input
                        id={`responsible-${activity.id}`}
                        value={activity.responsiblePerson || ""}
                        onChange={(e) => handleActivityChange(activity.id, "responsiblePerson", e.target.value)}
                        placeholder={t('programs.responsiblePlaceholder')}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`description-${activity.id}`}>{t('programs.description')}</Label>
                      <Textarea
                        id={`description-${activity.id}`}
                        value={activity.description || ""}
                        onChange={(e) => handleActivityChange(activity.id, "description", e.target.value)}
                        placeholder={t('programs.descriptionPlaceholder')}
                        rows={3}
                        className="resize-y"
                      />
                    </div>
                  </div>
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={handleAddActivity}
                className="w-full mt-2"
              >
                <Plus className="h-4 w-4 mr-2" />
                {t('programs.addActivity')}
              </Button>
            </div>
          </div>
          <DialogFooter className="pt-2 border-t mt-2">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleSaveProgram}>
              {editMode ? t('programs.saveChanges') : t('programs.createProgram')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Programs;
