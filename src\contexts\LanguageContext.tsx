import React, { createContext, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

type Language = 'en' | 'fr';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  toggleLanguage: () => void;
  detectBrowserLanguage: () => Language;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const LanguageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { i18n } = useTranslation();
  const [language, setLanguageState] = useState<Language>(() => {
    // Get the current language from i18n (which already handles detection)
    const currentLang = i18n.language;

    // Check if it's one of our supported languages
    if (currentLang === 'en' || currentLang === 'fr') {
      return currentLang as Language;
    }

    // If the language code has a region suffix (e.g., 'en-US', 'fr-CA')
    // try to match the language part
    const langWithoutRegion = currentLang.split('-')[0];
    if (langWithoutRegion === 'en' || langWithoutRegion === 'fr') {
      return langWithoutRegion as Language;
    }

    return 'fr'; // Default to French if no match
  });

  // Update i18n language when language state changes
  useEffect(() => {
    i18n.changeLanguage(language);
    localStorage.setItem('language', language);
  }, [language, i18n]);

  // Set language based on browser language on first load
  useEffect(() => {
    // Only apply if there's no language already set in localStorage
    const savedLanguage = localStorage.getItem('language');
    if (!savedLanguage) {
      const browserLang = detectBrowserLanguage();
      setLanguageState(browserLang);

      // Store that we've detected the language
      localStorage.setItem('languageDetected', 'true');
    }
  }, []);

  const setLanguage = (lang: Language) => {
    setLanguageState(lang);
  };

  const toggleLanguage = () => {
    setLanguageState(prevLang => (prevLang === 'en' ? 'fr' : 'en'));
  };

  // Function to detect browser language and return a supported language
  const detectBrowserLanguage = (): Language => {
    // Get browser language
    const browserLang = navigator.language.split('-')[0];

    // Check if it's one of our supported languages
    if (browserLang === 'en' || browserLang === 'fr') {
      return browserLang as Language;
    }

    return 'fr'; // Default to French if not supported
  };

  return (
    <LanguageContext.Provider value={{
      language,
      setLanguage,
      toggleLanguage,
      detectBrowserLanguage
    }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
