[1mdiff --git a/src/pages/Assemblies.tsx b/src/pages/Assemblies.tsx[m
[1mindex 15fb061..91c5788 100644[m
[1m--- a/src/pages/Assemblies.tsx[m
[1m+++ b/src/pages/Assemblies.tsx[m
[36m@@ -1,7 +1,14 @@[m
 [m
 import { Button } from "@/components/ui/button";[m
 import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";[m
[32m+[m[32mimport {[m
[32m+[m[32m  Dialog,[m
[32m+[m[32m  DialogContent,[m
[32m+[m[32m  DialogHeader,[m
[32m+[m[32m  DialogTitle,[m
[32m+[m[32m} from "@/components/ui/dialog";[m
 import { Input } from "@/components/ui/input";[m
[32m+[m[32mimport { ScrollArea } from "@/components/ui/scroll-area";[m
 import {[m
   Select,[m
   SelectContent,[m
[36m@@ -17,14 +24,26 @@[m [mimport {[m
   TableHeader,[m
   TableRow,[m
 } from "@/components/ui/table";[m
[31m-import { useData } from "@/contexts/DataContext";[m
[32m+[m[32mimport { useSupabaseData } from "@/contexts/SupabaseDataContext";[m
 import { toast } from "@/hooks/use-toast";[m
[31m-import { PlusCircle } from "lucide-react";[m
[31m-import React, { useState } from "react";[m
[32m+[m[32mimport { Building, ChevronLeft, ChevronRight, PlusCircle, RefreshCw, Users } from "lucide-react";[m
[32m+[m[32mimport React, { useEffect, useState } from "react";[m
 import { useTranslation } from "react-i18next";[m
 [m
 const Assemblies = () => {[m
[31m-  const { assemblies, districts, regions, addAssembly, getDistrictById, getRegionById, getDistrictsByRegion } = useData();[m
[32m+[m[32m  const {[m
[32m+[m[32m    assemblies,[m
[32m+[m[32m    districts,[m
[32m+[m[32m    regions,[m
[32m+[m[32m    addAssembly,[m
[32m+[m[32m    getDistrictById,[m
[32m+[m[32m    getRegionById,[m
[32m+[m[32m    getDistrictsByRegion,[m
[32m+[m[32m    getYouthsByAssembly,[m
[32m+[m[32m    isLoading,[m
[32m+[m[32m    refreshData[m
[32m+[m[32m  } = useSupabaseData();[m
[32m+[m
   const { t } = useTranslation();[m
   const [newAssemblyName, setNewAssemblyName] = useState("");[m
   const [selectedRegion, setSelectedRegion] = useState("");[m
[36m@@ -32,6 +51,14 @@[m [mconst Assemblies = () => {[m
   const [filteredDistricts, setFilteredDistricts] = useState(districts);[m
   const [errors, setErrors] = useState<Record<string, string>>({});[m
 [m
[32m+[m[32m  // State for assembly details dialog[m
[32m+[m[32m  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);[m
[32m+[m[32m  const [selectedAssemblyId, setSelectedAssemblyId] = useState<string | null>(null);[m
[32m+[m
[32m+[m[32m  // Pagination state for youth list in details dialog[m
[32m+[m[32m  const [currentPage, setCurrentPage] = useState(1);[m
[32m+[m[32m  const itemsPerPage = 20; // Number of youths to display per page[m
[32m+[m
   // Update districts when region changes[m
   const handleRegionChange = (value: string) => {[m
     setSelectedRegion(value);[m
[36m@@ -99,9 +126,53 @@[m [mconst Assemblies = () => {[m
     }[m
   };[m
 [m
[32m+[m[32m  const handleShowDetails = (assemblyId: string) => {[m
[32m+[m[32m    setSelectedAssemblyId(assemblyId);[m
[32m+[m[32m    setIsDetailsDialogOpen(true);[m
[32m+[m[32m    setCurrentPage(1); // Reset to first page when opening details[m
[32m+[m[32m  };[m
[32m+[m
[32m+[m[32m  // Reset pagination when dialog closes[m
[32m+[m[32m  useEffect(() => {[m
[32m+[m[32m    if (!isDetailsDialogOpen) {[m
[32m+[m[32m      setCurrentPage(1);[m
[32m+[m[32m    }[m
[32m+[m[32m  }, [isDetailsDialogOpen]);[m
[32m+[m
[32m+[m[32m  // Get the selected assembly object[m
[32m+[m[32m  const selectedAssembly = selectedAssemblyId[m
[32m+[m[32m    ? assemblies.find(assembly => assembly.id === selectedAssemblyId)[m
[32m+[m[32m    : null;[m
[32m+[m
[32m+[m[32m  // Get district and region for the selected assembly[m
[32m+[m[32m  const selectedAssemblyDistrict = selectedAssembly[m
[32m+[m[32m    ? getDistrictById(selectedAssembly.districtId)[m
[32m+[m[32m    : null;[m
[32m+[m
[32m+[m[32m  const selectedAssemblyRegion = selectedAssemblyDistrict[m
[32m+[m[32m    ? getRegionById(selectedAssemblyDistrict.regionId)[m
[32m+[m[32m    : null;[m
[32m+[m
[32m+[m[32m  // Get youths for the selected assembly[m
[32m+[m[32m  const assemblyYouths = selectedAssemblyId[m
[32m+[m[32m    ? getYouthsByAssembly(selectedAssemblyId)[m
[32m+[m[32m    : [];[m
[32m+[m
[32m+[m[32m  // Calculate pagination for youth list[m
[32m+[m[32m  const totalPages = Math.ceil(assemblyYouths.length / itemsPerPage);[m
[32m+[m[32m  const indexOfLastItem = currentPage * itemsPerPage;[m
[32m+[m[32m  const indexOfFirstItem = indexOfLastItem - itemsPerPage;[m
[32m+[m[32m  const currentYouths = assemblyYouths.slice(indexOfFirstItem, indexOfLastItem);[m
[32m+[m
   return ([m
     <div>[m
[31m-      <h1 className="text-2xl font-bold mb-6">{t('assemblies.title')}</h1>[m
[32m+[m[32m      <div className="flex justify-between items-center mb-6">[m
[32m+[m[32m        <h1 className="text-2xl font-bold">{t('assemblies.title')}</h1>[m
[32m+[m[32m        <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.assemblies}>[m
[32m+[m[32m          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.assemblies ? 'animate-spin' : ''}`} />[m
[32m+[m[32m          {t('common.refresh')}[m
[32m+[m[32m        </Button>[m
[32m+[m[32m      </div>[m
 [m
       <div className="grid grid-cols-1 md:grid-cols-2 gap-6">[m
         <Card>[m
[36m@@ -210,7 +281,11 @@[m [mconst Assemblies = () => {[m
                           <TableCell>{district?.name}</TableCell>[m
                           <TableCell>{region?.name}</TableCell>[m
                           <TableCell>[m
[31m-                            <Button variant="ghost" size="sm">[m
[32m+[m[32m                            <Button[m
[32m+[m[32m                              variant="ghost"[m
[32m+[m[32m                              size="sm"[m
[32m+[m[32m                              onClick={() => handleShowDetails(assembly.id)}[m
[32m+[m[32m                            >[m
                               {t('assemblies.details')}[m
                             </Button>[m
                           </TableCell>[m
[36m@@ -224,6 +299,97 @@[m [mconst Assemblies = () => {[m
           </CardContent>[m
         </Card>[m
       </div>[m
[32m+[m
[32m+[m[32m      {/* Assembly Details Dialog */}[m
[32m+[m[32m      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>[m
[32m+[m[32m        <DialogContent className="max-w-md max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>[m
[32m+[m[32m          <DialogHeader>[m
[32m+[m[32m            <DialogTitle>{t('assemblies.assemblyDetails')}</DialogTitle>[m
[32m+[m[32m          </DialogHeader>[m
[32m+[m
[32m+[m[32m          {selectedAssembly && ([m
[32m+[m[32m            <div className="space-y-6 py-4 overflow-y-auto">[m
[32m+[m[32m              <div className="space-y-2">[m
[32m+[m[32m                <div className="flex items-center gap-2">[m
[32m+[m[32m                  <Building className="h-5 w-5 text-primary" />[m
[32m+[m[32m                  <h3 className="text-lg font-medium">{selectedAssembly.name}</h3>[m
[32m+[m[32m                </div>[m
[32m+[m[32m                <div className="space-y-1 text-sm text-gray-500">[m
[32m+[m[32m                  <p>[m
[32m+[m[32m                    {t('assemblies.district')}: {selectedAssemblyDistrict?.name}[m
[32m+[m[32m                  </p>[m
[32m+[m[32m                  <p>[m
[32m+[m[32m                    {t('districts.region')}: {selectedAssemblyRegion?.name}[m
[32m+[m[32m                  </p>[m
[32m+[m[32m                </div>[m
[32m+[m[32m              </div>[m
[32m+[m
[32m+[m[32m              <div className="space-y-2">[m
[32m+[m[32m                <div className="flex items-center gap-2">[m
[32m+[m[32m                  <Users className="h-5 w-5 text-primary" />[m
[32m+[m[32m                  <h3 className="text-md font-medium">{t('youths.title')}</h3>[m
[32m+[m[32m                </div>[m
[32m+[m[32m                <p className="text-sm">[m
[32m+[m[32m                  {t('common.total')}: <span className="font-medium">{assemblyYouths.length}</span>[m
[32m+[m[32m                </p>[m
[32m+[m[32m                <div className="flex gap-4 text-sm">[m
[32m+[m[32m                  <p>[m
[32m+[m[32m                    {t('common.male')}: <span className="font-medium">[m
[32m+[m[32m                      {assemblyYouths.filter(youth => youth.gender === 'male').length}[m
[32m+[m[32m                    </span>[m
[32m+[m[32m                  </p>[m
[32m+[m[32m                  <p>[m
[32m+[m[32m                    {t('common.female')}: <span className="font-medium">[m
[32m+[m[32m                      {assemblyYouths.filter(youth => youth.gender === 'female').length}[m
[32m+[m[32m                    </span>[m
[32m+[m[32m                  </p>[m
[32m+[m[32m                </div>[m
[32m+[m
[32m+[m[32m                {assemblyYouths.length > 0 && ([m
[32m+[m[32m                  <div className="mt-4">[m
[32m+[m[32m                    <h4 className="text-sm font-medium mb-2">{t('youths.youthList')}:</h4>[m
[32m+[m[32m                    <ScrollArea className="h-[200px] pr-4">[m
[32m+[m[32m                      <ul className="space-y-1 pl-7 list-disc text-sm">[m
[32m+[m[32m                        {currentYouths.map(youth => ([m
[32m+[m[32m                          <li key={youth.id}>{youth.firstName} {youth.lastName}</li>[m
[32m+[m[32m                        ))}[m
[32m+[m[32m                      </ul>[m
[32m+[m[32m                    </ScrollArea>[m
[32m+[m
[32m+[m[32m                    {/* Pagination controls */}[m
[32m+[m[32m                    {totalPages > 1 && ([m
[32m+[m[32m                      <div className="flex justify-between items-center mt-4 text-sm">[m
[32m+[m[32m                        <div>[m
[32m+[m[32m                          {t('common.showing')} {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, assemblyYouths.length)} {t('common.of')} {assemblyYouths.length}[m
[32m+[m[32m                        </div>[m
[32m+[m[32m                        <div className="flex items-center gap-2">[m
[32m+[m[32m                          <Button[m
[32m+[m[32m                            variant="outline"[m
[32m+[m[32m                            size="sm"[m
[32m+[m[32m                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}[m
[32m+[m[32m                            disabled={currentPage === 1}[m
[32m+[m[32m                          >[m
[32m+[m[32m                            <ChevronLeft className="h-4 w-4" />[m
[32m+[m[32m                          </Button>[m
[32m+[m[32m                          <span>{currentPage} / {totalPages}</span>[m
[32m+[m[32m                          <Button[m
[32m+[m[32m                            variant="outline"[m
[32m+[m[32m                            size="sm"[m
[32m+[m[32m                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}[m
[32m+[m[32m                            disabled={currentPage === totalPages}[m
[32m+[m[32m                          >[m
[32m+[m[32m                            <ChevronRight className="h-4 w-4" />[m
[32m+[m[32m                          </Button>[m
[32m+[m[32m                        </div>[m
[32m+[m[32m                      </div>[m
[32m+[m[32m                    )}[m
[32m+[m[32m                  </div>[m
[32m+[m[32m                )}[m
[32m+[m[32m              </div>[m
[32m+[m[32m            </div>[m
[32m+[m[32m          )}[m
[32m+[m[32m        </DialogContent>[m
[32m+[m[32m      </Dialog>[m
     </div>[m
   );[m
 };[m
