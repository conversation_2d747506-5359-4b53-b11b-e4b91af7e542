import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SupabaseDataProvider } from '@/contexts/SupabaseDataContext';
import { SupabaseRealTimeProvider } from '@/contexts/SupabaseRealTimeContext';
import SupabaseRegions from '@/pages/SupabaseRegions';
import SupabaseDistricts from '@/pages/SupabaseDistricts';
import SupabaseAssemblies from '@/pages/SupabaseAssemblies';
import SupabaseYouthList from '@/pages/SupabaseYouthList';
import SupabaseYouthDetails from '@/pages/SupabaseYouthDetails';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/i18n';

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    channel: jest.fn().mockReturnValue({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn().mockReturnValue({
        unsubscribe: jest.fn()
      })
    }),
    from: jest.fn().mockReturnValue({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      then: jest.fn().mockImplementation(callback => Promise.resolve(callback({ data: [], error: null })))
    })
  }
}));

// Mock the toast component
jest.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn()
  })
}));

// Mock the services
jest.mock('@/services', () => ({
  regionsService: {
    getAll: jest.fn().mockResolvedValue([
      { id: '1', name: 'Region 1' },
      { id: '2', name: 'Region 2' }
    ]),
    create: jest.fn().mockImplementation((data) => Promise.resolve({ id: '3', ...data })),
    delete: jest.fn().mockResolvedValue(undefined)
  },
  districtsService: {
    getAll: jest.fn().mockResolvedValue([
      { id: '1', name: 'District 1', regionId: '1' },
      { id: '2', name: 'District 2', regionId: '2' }
    ]),
    create: jest.fn().mockImplementation((data) => Promise.resolve({ id: '3', ...data })),
    delete: jest.fn().mockResolvedValue(undefined)
  },
  assembliesService: {
    getAll: jest.fn().mockResolvedValue([
      { id: '1', name: 'Assembly 1', districtId: '1' },
      { id: '2', name: 'Assembly 2', districtId: '2' }
    ]),
    create: jest.fn().mockImplementation((data) => Promise.resolve({ id: '3', ...data })),
    delete: jest.fn().mockResolvedValue(undefined)
  },
  youthsService: {
    getAll: jest.fn().mockResolvedValue([
      { id: '1', firstName: 'John', lastName: 'Doe', gender: 'male', assemblyId: '1', registrationDate: new Date() },
      { id: '2', firstName: 'Jane', lastName: 'Doe', gender: 'female', assemblyId: '2', registrationDate: new Date() }
    ]),
    getById: jest.fn().mockImplementation((id) => {
      if (id === '1') {
        return Promise.resolve({
          id: '1',
          firstName: 'John',
          lastName: 'Doe',
          gender: 'male',
          assemblyId: '1',
          registrationDate: new Date()
        });
      }
      return Promise.resolve(null);
    }),
    create: jest.fn().mockImplementation((data) => Promise.resolve({ id: '3', ...data })),
    update: jest.fn().mockImplementation((id, data) => Promise.resolve({ id, ...data })),
    delete: jest.fn().mockResolvedValue(undefined)
  },
  paymentsService: {
    getAll: jest.fn().mockResolvedValue([
      { id: '1', youthId: '1', amount: 1000, method: 'cash', date: new Date() },
      { id: '2', youthId: '2', amount: 2000, method: 'mobile_money', date: new Date() }
    ]),
    create: jest.fn().mockImplementation((data) => Promise.resolve({ id: '3', ...data })),
    delete: jest.fn().mockResolvedValue(undefined)
  }
}));

// Mock the useParams hook
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ youthId: '1' }),
  useNavigate: () => jest.fn()
}));

// Setup test wrapper
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    <I18nextProvider i18n={i18n}>
      <BrowserRouter>
        <SupabaseRealTimeProvider>
          <SupabaseDataProvider>
            {children}
          </SupabaseDataProvider>
        </SupabaseRealTimeProvider>
      </BrowserRouter>
    </I18nextProvider>
  </QueryClientProvider>
);

describe('SupabaseRegions Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the regions list', async () => {
    render(
      <TestWrapper>
        <SupabaseRegions />
      </TestWrapper>
    );

    // Wait for the regions to load
    await waitFor(() => {
      expect(screen.getByText('Region 1')).toBeInTheDocument();
      expect(screen.getByText('Region 2')).toBeInTheDocument();
    });
  });

  test('adds a new region', async () => {
    render(
      <TestWrapper>
        <SupabaseRegions />
      </TestWrapper>
    );

    // Fill in the form
    const input = screen.getByPlaceholderText(/region name/i);
    fireEvent.change(input, { target: { value: 'New Region' } });

    // Submit the form
    const addButton = screen.getByText(/add/i);
    fireEvent.click(addButton);

    // Check if the service was called
    await waitFor(() => {
      expect(require('@/services').regionsService.create).toHaveBeenCalledWith({ name: 'New Region' });
    });
  });

  test('filters regions by search term', async () => {
    render(
      <TestWrapper>
        <SupabaseRegions />
      </TestWrapper>
    );

    // Wait for the regions to load
    await waitFor(() => {
      expect(screen.getByText('Region 1')).toBeInTheDocument();
      expect(screen.getByText('Region 2')).toBeInTheDocument();
    });

    // Search for a region
    const searchInput = screen.getByPlaceholderText(/search/i);
    fireEvent.change(searchInput, { target: { value: 'Region 1' } });

    // Check if the filter works
    await waitFor(() => {
      expect(screen.getByText('Region 1')).toBeInTheDocument();
      expect(screen.queryByText('Region 2')).not.toBeInTheDocument();
    });
  });
});

describe('SupabaseDistricts Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the districts list', async () => {
    render(
      <TestWrapper>
        <SupabaseDistricts />
      </TestWrapper>
    );

    // Wait for the districts to load
    await waitFor(() => {
      expect(screen.getByText('District 1')).toBeInTheDocument();
      expect(screen.getByText('District 2')).toBeInTheDocument();
    });
  });

  test('filters districts by region', async () => {
    render(
      <TestWrapper>
        <SupabaseDistricts />
      </TestWrapper>
    );

    // Wait for the districts to load
    await waitFor(() => {
      expect(screen.getByText('District 1')).toBeInTheDocument();
      expect(screen.getByText('District 2')).toBeInTheDocument();
    });

    // Select a region filter
    const regionSelect = screen.getByText(/filter by region/i);
    fireEvent.click(regionSelect);
    
    // This part would need to be adjusted based on how your select component works
    // For now, we'll just check if the select was clicked
    expect(regionSelect).toBeInTheDocument();
  });
});

describe('SupabaseYouthList Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the youth list', async () => {
    render(
      <TestWrapper>
        <SupabaseYouthList />
      </TestWrapper>
    );

    // Wait for the youths to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Jane Doe')).toBeInTheDocument();
    });
  });
});

describe('SupabaseYouthDetails Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the youth details', async () => {
    render(
      <TestWrapper>
        <SupabaseYouthDetails />
      </TestWrapper>
    );

    // Wait for the youth details to load
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });
});
