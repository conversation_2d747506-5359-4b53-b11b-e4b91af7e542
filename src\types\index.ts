
export type Gender = 'male' | 'female';

export interface Region {
  id: string;
  name: string;
}

export interface District {
  id: string;
  name: string;
  regionId: string;
}

export interface Assembly {
  id: string;
  name: string;
  districtId: string;
}

export interface Youth {
  id: string;
  firstName: string;
  lastName: string;
  gender: Gender;
  assemblyId: string;
  payments: Payment[];
  registrationDate: Date;
}

export interface Payment {
  id: string;
  youthId: string;
  amount: number;
  date: Date;
  method: 'cash' | 'mobile_money' | 'bank_transfer' | 'other';
  reference?: string;
}

export interface Permission {
  id: string;
  youthId: string;
  reason: string;
  startDate: Date;
  endDate: Date;
  approved: boolean;
  approvedBy?: string;
}

export interface Sermon {
  id: string;
  preacher: string;
  date: Date;
  theme: string;
  passages: string[];
  content: string;
}

export interface DailyProgram {
  id: string;
  date: Date;
  activities: ProgramActivity[];
}

export interface ProgramActivity {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  responsiblePerson?: string;
}

export interface User {
  id: string;
  username: string;
  role: 'super-admin' | 'admin' | 'moderator' | 'viewer';
  email: string;
  fullName: string;
}

export interface RegistrationLink {
  id: string;
  url: string;
  expiresAt?: Date;
  maxRegistrations?: number;
  currentRegistrations: number;
  createdAt: Date;
}

export interface ProgramShareLink {
  id: string;
  programId: string;
  url: string;
  createdAt: Date;
  viewCount: number;
}

export type FeatureType =
  | 'youths'
  | 'payments'
  | 'permissions'
  | 'programs'
  | 'sermons'
  | 'regions'
  | 'districts'
  | 'assemblies';

export type AccessType = 'read' | 'write' | 'read-write';

export interface AccessRight {
  id: string;
  name: string;
  description?: string;
  features: {
    feature: FeatureType;
    access: AccessType;
  }[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export interface MagicLink {
  id: string;
  accessRightId: string;
  url: string;
  name: string;
  expiresAt?: Date;
  createdAt: Date;
  lastUsedAt?: Date;
  viewCount: number;
  editCount: number;
  active: boolean;
}

export interface AccessLog {
  id: string;
  magicLinkId: string;
  userId?: string;
  action: 'view' | 'edit';
  feature: FeatureType;
  timestamp: Date;
  details?: string;
}
