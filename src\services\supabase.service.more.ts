import { supabase } from '@/lib/supabase';
import {
  DailyProgram,
  Permission,
  ProgramActivity,
  Sermon
} from '@/types';

// Import helper functions
import {
  convertKeysToCamel,
  convertKeysToSnake
} from './supabase.service.helpers';

// Permissions Service
export const permissionsService = {
  async getAll(): Promise<Permission[]> {
    const { data, error } = await supabase
      .from('permissions')
      .select('*')
      .order('start_date', { ascending: false });

    if (error) {
      console.error('Error fetching permissions:', error);
      throw error;
    }

    return data.map(permission => {
      const converted = convertKeysToCamel(permission);
      return {
        ...converted,
        startDate: new Date(converted.startDate),
        endDate: new Date(converted.endDate)
      } as Permission;
    });
  },

  async getById(id: string): Promise<Permission | null> {
    const { data, error } = await supabase
      .from('permissions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching permission with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      startDate: new Date(converted.startDate),
      endDate: new Date(converted.endDate)
    } as Permission;
  },

  async getByYouth(youthId: string): Promise<Permission[]> {
    const { data, error } = await supabase
      .from('permissions')
      .select('*')
      .eq('youth_id', youthId)
      .order('start_date', { ascending: false });

    if (error) {
      console.error(`Error fetching permissions for youth ${youthId}:`, error);
      throw error;
    }

    return data.map(permission => {
      const converted = convertKeysToCamel(permission);
      return {
        ...converted,
        startDate: new Date(converted.startDate),
        endDate: new Date(converted.endDate)
      } as Permission;
    });
  },

  async create(permission: Omit<Permission, 'id'>): Promise<Permission> {
    const permissionToInsert = {
      ...convertKeysToSnake(permission),
      start_date: permission.startDate.toISOString(),
      end_date: permission.endDate.toISOString()
    };

    const { data, error } = await supabase
      .from('permissions')
      .insert(permissionToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating permission:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      startDate: new Date(converted.startDate),
      endDate: new Date(converted.endDate)
    } as Permission;
  },

  async update(id: string, permission: Partial<Omit<Permission, 'id'>>): Promise<Permission> {
    const permissionToUpdate: any = convertKeysToSnake(permission);

    if (permission.startDate) {
      permissionToUpdate.start_date = permission.startDate.toISOString();
    }

    if (permission.endDate) {
      permissionToUpdate.end_date = permission.endDate.toISOString();
    }

    const { data, error } = await supabase
      .from('permissions')
      .update(permissionToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating permission with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      startDate: new Date(converted.startDate),
      endDate: new Date(converted.endDate)
    } as Permission;
  },

  async approve(id: string, approvedBy: string): Promise<Permission> {
    const { data, error } = await supabase
      .from('permissions')
      .update({
        approved: true,
        approved_by: approvedBy
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error approving permission with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      startDate: new Date(converted.startDate),
      endDate: new Date(converted.endDate)
    } as Permission;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('permissions')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting permission with id ${id}:`, error);
      throw error;
    }
  }
};

// Sermons Service
export const sermonsService = {
  async getAll(): Promise<Sermon[]> {
    const { data, error } = await supabase
      .from('sermons')
      .select('*')
      .order('date');

    if (error) {
      console.error('Error fetching sermons:', error);
      throw error;
    }

    return data.map(sermon => {
      const converted = convertKeysToCamel(sermon);
      return {
        ...converted,
        date: new Date(converted.date)
      } as Sermon;
    });
  },

  async getById(id: string): Promise<Sermon | null> {
    const { data, error } = await supabase
      .from('sermons')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching sermon with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date)
    } as Sermon;
  },

  async create(sermon: Omit<Sermon, 'id'>): Promise<Sermon> {
    const sermonToInsert = {
      ...convertKeysToSnake(sermon),
      date: sermon.date.toISOString()
    };

    const { data, error } = await supabase
      .from('sermons')
      .insert(sermonToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating sermon:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date)
    } as Sermon;
  },

  async update(id: string, sermon: Partial<Omit<Sermon, 'id'>>): Promise<Sermon> {
    const sermonToUpdate: any = convertKeysToSnake(sermon);

    if (sermon.date) {
      sermonToUpdate.date = sermon.date.toISOString();
    }

    const { data, error } = await supabase
      .from('sermons')
      .update(sermonToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating sermon with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date)
    } as Sermon;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('sermons')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting sermon with id ${id}:`, error);
      throw error;
    }
  }
};

// Daily Programs Service
export const dailyProgramsService = {
  async getAll(): Promise<DailyProgram[]> {
    // First get all daily programs
    const { data, error } = await supabase
      .from('daily_programs')
      .select('*')
      .order('date');

    if (error) {
      console.error('Error fetching daily programs:', error);
      throw error;
    }

    // Then get all activities for each program
    const programs = await Promise.all(data.map(async program => {
      const { data: activitiesData, error: activitiesError } = await supabase
        .from('program_activities')
        .select('*')
        .eq('program_id', program.id)
        .order('start_time');

      if (activitiesError) {
        console.error(`Error fetching activities for program ${program.id}:`, activitiesError);
        throw activitiesError;
      }

      const activities = activitiesData.map(activity => convertKeysToCamel(activity)) as ProgramActivity[];

      const converted = convertKeysToCamel(program);

      return {
        ...converted,
        date: new Date(converted.date),
        activities
      } as DailyProgram;
    }));

    return programs;
  },

  async getById(id: string): Promise<DailyProgram | null> {
    const { data, error } = await supabase
      .from('daily_programs')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching daily program with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    // Get activities for this program
    const { data: activitiesData, error: activitiesError } = await supabase
      .from('program_activities')
      .select('*')
      .eq('program_id', id)
      .order('start_time');

    if (activitiesError) {
      console.error(`Error fetching activities for program ${id}:`, activitiesError);
      throw activitiesError;
    }

    const activities = activitiesData.map(activity => convertKeysToCamel(activity)) as ProgramActivity[];

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      date: new Date(converted.date),
      activities
    } as DailyProgram;
  },

  async create(program: Omit<DailyProgram, 'id'>): Promise<DailyProgram> {
    // First create the daily program
    const { data, error } = await supabase
      .from('daily_programs')
      .insert({
        date: program.date.toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating daily program:', error);
      throw error;
    }

    const programId = data.id;

    // Then create the activities
    if (program.activities && program.activities.length > 0) {
      const activitiesToInsert = program.activities.map(activity => ({
        program_id: programId,
        title: activity.title,
        description: activity.description,
        start_time: activity.startTime,
        end_time: activity.endTime,
        responsible_person: activity.responsiblePerson
      }));

      const { error: activitiesError } = await supabase
        .from('program_activities')
        .insert(activitiesToInsert);

      if (activitiesError) {
        console.error('Error creating program activities:', activitiesError);
        throw activitiesError;
      }
    }

    // Return the created program with activities
    return this.getById(programId) as Promise<DailyProgram>;
  },

  async update(id: string, updates: Partial<Omit<DailyProgram, 'id'>>): Promise<DailyProgram> {
    const updateData: any = {};

    if (updates.date) {
      updateData.date = updates.date.toISOString();
    }

    // Update the daily program
    const { error } = await supabase
      .from('daily_programs')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error(`Error updating daily program with id ${id}:`, error);
      throw error;
    }

    // Return the updated program with activities
    return this.getById(id) as Promise<DailyProgram>;
  }
};

// Program Activities Service
export const programActivitiesService = {
  async getByProgramId(programId: string): Promise<ProgramActivity[]> {
    const { data, error } = await supabase
      .from('program_activities')
      .select('*')
      .eq('program_id', programId)
      .order('start_time');

    if (error) {
      console.error(`Error fetching activities for program ${programId}:`, error);
      throw error;
    }

    return data.map(activity => convertKeysToCamel(activity)) as ProgramActivity[];
  },

  async create(activity: Omit<ProgramActivity, 'id'>): Promise<ProgramActivity> {
    const activityToInsert = {
      program_id: activity.programId,
      title: activity.title,
      description: activity.description,
      start_time: activity.startTime,
      end_time: activity.endTime,
      responsible_person: activity.responsiblePerson
    };

    const { data, error } = await supabase
      .from('program_activities')
      .insert(activityToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating program activity:', error);
      throw error;
    }

    return convertKeysToCamel(data) as ProgramActivity;
  },

  async update(id: string, updates: Partial<Omit<ProgramActivity, 'id'>>): Promise<ProgramActivity> {
    const activityToUpdate = convertKeysToSnake(updates);

    const { data, error } = await supabase
      .from('program_activities')
      .update(activityToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating program activity with id ${id}:`, error);
      throw error;
    }

    return convertKeysToCamel(data) as ProgramActivity;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('program_activities')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting program activity with id ${id}:`, error);
      throw error;
    }
  },

  async updateActivities(programId: string, activities: ProgramActivity[]): Promise<ProgramActivity[]> {
    // First delete all existing activities for this program
    const { error: deleteError } = await supabase
      .from('program_activities')
      .delete()
      .eq('program_id', programId);

    if (deleteError) {
      console.error(`Error deleting activities for program ${programId}:`, deleteError);
      throw deleteError;
    }

    // Then create the new activities
    if (activities.length > 0) {
      const activitiesToInsert = activities.map(activity => ({
        program_id: programId,
        title: activity.title,
        description: activity.description,
        start_time: activity.startTime,
        end_time: activity.endTime,
        responsible_person: activity.responsiblePerson
      }));

      const { data, error } = await supabase
        .from('program_activities')
        .insert(activitiesToInsert)
        .select();

      if (error) {
        console.error('Error creating program activities:', error);
        throw error;
      }

      return data.map(activity => convertKeysToCamel(activity)) as ProgramActivity[];
    }

    return [];
  }
};
