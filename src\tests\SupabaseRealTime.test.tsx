import React from 'react';
import { render, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SupabaseRealTimeProvider, useSupabaseRealTime } from '@/contexts/SupabaseRealTimeContext';

// Mock the Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    channel: jest.fn().mockReturnValue({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn().mockReturnValue({
        unsubscribe: jest.fn()
      })
    })
  }
}));

// Setup test wrapper
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <QueryClientProvider client={queryClient}>
    <SupabaseRealTimeProvider>
      {children}
    </SupabaseRealTimeProvider>
  </QueryClientProvider>
);

// Test component that uses the real-time context
const TestComponent = () => {
  const { isRealTimeEnabled, enableRealTime, disableRealTime } = useSupabaseRealTime();
  
  return (
    <div>
      <div data-testid="status">{isRealTimeEnabled ? 'enabled' : 'disabled'}</div>
      <button data-testid="enable" onClick={enableRealTime}>Enable</button>
      <button data-testid="disable" onClick={disableRealTime}>Disable</button>
    </div>
  );
};

describe('SupabaseRealTimeContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('provides real-time state and methods', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );
    
    // Real-time should be enabled by default
    expect(getByTestId('status').textContent).toBe('enabled');
  });

  test('can disable real-time updates', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );
    
    // Disable real-time updates
    act(() => {
      getByTestId('disable').click();
    });
    
    // Real-time should now be disabled
    expect(getByTestId('status').textContent).toBe('disabled');
  });

  test('can re-enable real-time updates', () => {
    const { getByTestId } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );
    
    // Disable real-time updates
    act(() => {
      getByTestId('disable').click();
    });
    
    // Real-time should now be disabled
    expect(getByTestId('status').textContent).toBe('disabled');
    
    // Re-enable real-time updates
    act(() => {
      getByTestId('enable').click();
    });
    
    // Real-time should now be enabled again
    expect(getByTestId('status').textContent).toBe('enabled');
  });

  test('sets up subscriptions when enabled', () => {
    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );
    
    // Check if the Supabase channel was created
    expect(require('@/lib/supabase').supabase.channel).toHaveBeenCalled();
  });

  test('cleans up subscriptions on unmount', () => {
    const { unmount } = render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );
    
    // Get the unsubscribe function
    const unsubscribe = require('@/lib/supabase').supabase.channel().subscribe().unsubscribe;
    
    // Unmount the component
    unmount();
    
    // Check if unsubscribe was called
    expect(unsubscribe).toHaveBeenCalled();
  });
});

describe('SupabaseRealTimeProvider', () => {
  test('subscribes to all tables', () => {
    render(
      <TestWrapper>
        <div />
      </TestWrapper>
    );
    
    // Check if the Supabase channel was created for each table
    const { supabase } = require('@/lib/supabase');
    
    // We expect multiple channel calls for different tables
    expect(supabase.channel).toHaveBeenCalled();
    expect(supabase.channel().on).toHaveBeenCalled();
    expect(supabase.channel().subscribe).toHaveBeenCalled();
  });
});
