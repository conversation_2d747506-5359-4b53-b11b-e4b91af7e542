import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useLanguage } from '@/contexts/LanguageContext';
import { Globe } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface LanguageSwitcherProps {
  showLabel?: boolean;
  className?: string;
}

const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({ showLabel = false, className = '' }) => {
  const { language, setLanguage, detectBrowserLanguage } = useLanguage();
  const { t, i18n } = useTranslation();

  const getLanguageLabel = () => {
    return language === 'fr' ? 'FR' : 'EN';
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size={showLabel ? "sm" : "icon"}
          className={`${showLabel ? 'px-2' : 'h-9 w-9 rounded-full'} ${className}`}
        >
          <Globe className="h-4 w-4" />
          {showLabel && <span className="ml-2">{getLanguageLabel()}</span>}
          <span className="sr-only">{t('common.language')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => setLanguage('fr')}
          className={language === 'fr' ? 'bg-primary/10 text-primary' : ''}
        >
          {t('common.french')}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setLanguage('en')}
          className={language === 'en' ? 'bg-primary/10 text-primary' : ''}
        >
          {t('common.english')}
        </DropdownMenuItem>

        {/* Browser language option */}
        {detectBrowserLanguage() !== language && (
          <>
            <div className="px-2 py-1.5 text-xs text-muted-foreground">
              {t('common.browserLanguage')}
            </div>
            <DropdownMenuItem
              onClick={() => setLanguage(detectBrowserLanguage())}
              className="flex items-center gap-2"
            >
              <Globe className="h-3.5 w-3.5" />
              {detectBrowserLanguage() === 'fr' ? t('common.french') : t('common.english')}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default LanguageSwitcher;
