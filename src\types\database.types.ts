export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      regions: {
        Row: {
          id: string
          name: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          created_at?: string
        }
        Relationships: []
      }
      districts: {
        Row: {
          id: string
          name: string
          region_id: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          region_id: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          region_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "districts_region_id_fkey"
            columns: ["region_id"]
            referencedRelation: "regions"
            referencedColumns: ["id"]
          }
        ]
      }
      assemblies: {
        Row: {
          id: string
          name: string
          district_id: string
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          district_id: string
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          district_id?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "assemblies_district_id_fkey"
            columns: ["district_id"]
            referencedRelation: "districts"
            referencedColumns: ["id"]
          }
        ]
      }
      youths: {
        Row: {
          id: string
          first_name: string
          last_name: string
          gender: string
          assembly_id: string
          registration_date: string
          created_at: string
        }
        Insert: {
          id?: string
          first_name: string
          last_name: string
          gender: string
          assembly_id: string
          registration_date: string
          created_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          last_name?: string
          gender?: string
          assembly_id?: string
          registration_date?: string
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "youths_assembly_id_fkey"
            columns: ["assembly_id"]
            referencedRelation: "assemblies"
            referencedColumns: ["id"]
          }
        ]
      }
      payments: {
        Row: {
          id: string
          youth_id: string
          amount: number
          date: string
          method: string
          reference: string | null
          created_at: string
        }
        Insert: {
          id?: string
          youth_id: string
          amount: number
          date: string
          method: string
          reference?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          youth_id?: string
          amount?: number
          date?: string
          method?: string
          reference?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "payments_youth_id_fkey"
            columns: ["youth_id"]
            referencedRelation: "youths"
            referencedColumns: ["id"]
          }
        ]
      }
      permissions: {
        Row: {
          id: string
          youth_id: string
          reason: string
          start_date: string
          end_date: string
          approved: boolean
          approved_by: string | null
          created_at: string
        }
        Insert: {
          id?: string
          youth_id: string
          reason: string
          start_date: string
          end_date: string
          approved: boolean
          approved_by?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          youth_id?: string
          reason?: string
          start_date?: string
          end_date?: string
          approved?: boolean
          approved_by?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "permissions_youth_id_fkey"
            columns: ["youth_id"]
            referencedRelation: "youths"
            referencedColumns: ["id"]
          }
        ]
      }
      sermons: {
        Row: {
          id: string
          preacher: string
          date: string
          theme: string
          passages: string[]
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          preacher: string
          date: string
          theme: string
          passages: string[]
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          preacher?: string
          date?: string
          theme?: string
          passages?: string[]
          content?: string
          created_at?: string
        }
        Relationships: []
      }
      daily_programs: {
        Row: {
          id: string
          date: string
          created_at: string
        }
        Insert: {
          id?: string
          date: string
          created_at?: string
        }
        Update: {
          id?: string
          date?: string
          created_at?: string
        }
        Relationships: []
      }
      program_activities: {
        Row: {
          id: string
          program_id: string
          title: string
          description: string | null
          start_time: string
          end_time: string
          responsible_person: string | null
          created_at: string
        }
        Insert: {
          id?: string
          program_id: string
          title: string
          description?: string | null
          start_time: string
          end_time: string
          responsible_person?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          program_id?: string
          title?: string
          description?: string | null
          start_time?: string
          end_time?: string
          responsible_person?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "program_activities_program_id_fkey"
            columns: ["program_id"]
            referencedRelation: "daily_programs"
            referencedColumns: ["id"]
          }
        ]
      }
      registration_links: {
        Row: {
          id: string
          url: string
          expires_at: string | null
          max_registrations: number | null
          current_registrations: number
          created_at: string
        }
        Insert: {
          id?: string
          url: string
          expires_at?: string | null
          max_registrations?: number | null
          current_registrations: number
          created_at?: string
        }
        Update: {
          id?: string
          url?: string
          expires_at?: string | null
          max_registrations?: number | null
          current_registrations?: number
          created_at?: string
        }
        Relationships: []
      }
      program_share_links: {
        Row: {
          id: string
          program_id: string
          url: string
          view_count: number
          created_at: string
        }
        Insert: {
          id?: string
          program_id: string
          url: string
          view_count: number
          created_at?: string
        }
        Update: {
          id?: string
          program_id?: string
          url?: string
          view_count?: number
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "program_share_links_program_id_fkey"
            columns: ["program_id"]
            referencedRelation: "daily_programs"
            referencedColumns: ["id"]
          }
        ]
      }
      access_rights: {
        Row: {
          id: string
          name: string
          description: string | null
          features: Json
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          features: Json
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          features?: Json
          created_at?: string
          updated_at?: string
          created_by?: string
        }
        Relationships: []
      }
      magic_links: {
        Row: {
          id: string
          access_right_id: string
          url: string
          name: string
          expires_at: string | null
          created_at: string
          last_used_at: string | null
          view_count: number
          edit_count: number
          active: boolean
        }
        Insert: {
          id?: string
          access_right_id: string
          url: string
          name: string
          expires_at?: string | null
          created_at?: string
          last_used_at?: string | null
          view_count: number
          edit_count: number
          active: boolean
        }
        Update: {
          id?: string
          access_right_id?: string
          url?: string
          name?: string
          expires_at?: string | null
          created_at?: string
          last_used_at?: string | null
          view_count?: number
          edit_count?: number
          active?: boolean
        }
        Relationships: [
          {
            foreignKeyName: "magic_links_access_right_id_fkey"
            columns: ["access_right_id"]
            referencedRelation: "access_rights"
            referencedColumns: ["id"]
          }
        ]
      }
      access_logs: {
        Row: {
          id: string
          magic_link_id: string
          user_id: string | null
          action: string
          feature: string
          timestamp: string
          details: string | null
          created_at: string
        }
        Insert: {
          id?: string
          magic_link_id: string
          user_id?: string | null
          action: string
          feature: string
          timestamp: string
          details?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          magic_link_id?: string
          user_id?: string | null
          action?: string
          feature?: string
          timestamp?: string
          details?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "access_logs_magic_link_id_fkey"
            columns: ["magic_link_id"]
            referencedRelation: "magic_links"
            referencedColumns: ["id"]
          }
        ]
      }
      users: {
        Row: {
          id: string
          email: string
          username: string
          full_name: string
          role: string
          created_at: string
        }
        Insert: {
          id?: string
          email: string
          username: string
          full_name: string
          role: string
          created_at?: string
        }
        Update: {
          id?: string
          email?: string
          username?: string
          full_name?: string
          role?: string
          created_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
