import { supabase } from '@/lib/supabase';
import { AccessLog, AccessRight, MagicLink } from '@/types';
import { v4 as uuidv4 } from 'uuid';
import { convertKeysToCamel, convertKeysToSnake } from './supabase.service.helpers';

// Access Rights Service
export const accessRightsService = {
  async getAll(): Promise<AccessRight[]> {
    const { data, error } = await supabase
      .from('access_rights')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching access rights:', error);
      throw error;
    }

    return data.map(right => {
      const converted = convertKeysToCamel(right);
      return {
        ...converted,
        createdAt: new Date(converted.createdAt),
        updatedAt: new Date(converted.updatedAt)
      } as AccessRight;
    });
  },

  async getById(id: string): Promise<AccessRight | null> {
    const { data, error } = await supabase
      .from('access_rights')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching access right with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      updatedAt: new Date(converted.updatedAt)
    } as AccessRight;
  },

  async create(accessRight: Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>): Promise<AccessRight> {
    const now = new Date();

    const accessRightToInsert = {
      ...convertKeysToSnake(accessRight),
      created_at: now.toISOString(),
      updated_at: now.toISOString()
    };

    const { data, error } = await supabase
      .from('access_rights')
      .insert(accessRightToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating access right:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      updatedAt: new Date(converted.updatedAt)
    } as AccessRight;
  },

  async update(id: string, updates: Partial<Omit<AccessRight, 'id' | 'createdAt' | 'updatedAt'>>): Promise<AccessRight> {
    const accessRightToUpdate = {
      ...convertKeysToSnake(updates),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('access_rights')
      .update(accessRightToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating access right with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      updatedAt: new Date(converted.updatedAt)
    } as AccessRight;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('access_rights')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting access right with id ${id}:`, error);
      throw error;
    }
  }
};

// Magic Links Service
export const magicLinksService = {
  async getAll(): Promise<MagicLink[]> {
    const { data, error } = await supabase
      .from('magic_links')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching magic links:', error);
      throw error;
    }

    return data.map(link => {
      const converted = convertKeysToCamel(link);
      return {
        ...converted,
        createdAt: new Date(converted.createdAt),
        expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
        lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
      } as MagicLink;
    });
  },

  async getById(id: string): Promise<MagicLink | null> {
    const { data, error } = await supabase
      .from('magic_links')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching magic link with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
      lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
    } as MagicLink;
  },

  async getByAccessRightId(accessRightId: string): Promise<MagicLink[]> {
    const { data, error } = await supabase
      .from('magic_links')
      .select('*')
      .eq('access_right_id', accessRightId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error(`Error fetching magic links for access right ${accessRightId}:`, error);
      throw error;
    }

    return data.map(link => {
      const converted = convertKeysToCamel(link);
      return {
        ...converted,
        createdAt: new Date(converted.createdAt),
        expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
        lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
      } as MagicLink;
    });
  },

  async create(magicLink: Omit<MagicLink, 'id' | 'createdAt'>): Promise<MagicLink> {
    // Generate a unique URL if not provided
    const url = magicLink.url || `/access/${uuidv4()}`;

    const magicLinkToInsert = {
      ...convertKeysToSnake(magicLink),
      url,
      expires_at: magicLink.expiresAt ? magicLink.expiresAt.toISOString() : null,
      last_used_at: null,
      view_count: 0,
      edit_count: 0,
      active: true
    };

    const { data, error } = await supabase
      .from('magic_links')
      .insert(magicLinkToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating magic link:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
      lastUsedAt: null
    } as MagicLink;
  },

  async update(id: string, updates: Partial<Omit<MagicLink, 'id' | 'createdAt'>>): Promise<MagicLink> {
    const magicLinkToUpdate: any = convertKeysToSnake(updates);

    if (updates.expiresAt) {
      magicLinkToUpdate.expires_at = updates.expiresAt.toISOString();
    }

    if (updates.lastUsedAt) {
      magicLinkToUpdate.last_used_at = updates.lastUsedAt.toISOString();
    }

    const { data, error } = await supabase
      .from('magic_links')
      .update(magicLinkToUpdate)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error updating magic link with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
      lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
    } as MagicLink;
  },

  async incrementViewCount(id: string): Promise<MagicLink> {
    // First get the current count
    const { data: currentData, error: fetchError } = await supabase
      .from('magic_links')
      .select('view_count')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error(`Error fetching magic link with id ${id}:`, fetchError);
      throw fetchError;
    }

    const currentCount = currentData.view_count;

    // Then update the count and last used time
    const { data, error } = await supabase
      .from('magic_links')
      .update({
        view_count: currentCount + 1,
        last_used_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error incrementing view count for magic link with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
      lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
    } as MagicLink;
  },

  async incrementEditCount(id: string): Promise<MagicLink> {
    // First get the current count
    const { data: currentData, error: fetchError } = await supabase
      .from('magic_links')
      .select('edit_count')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error(`Error fetching magic link with id ${id}:`, fetchError);
      throw fetchError;
    }

    const currentCount = currentData.edit_count;

    // Then update the count and last used time
    const { data, error } = await supabase
      .from('magic_links')
      .update({
        edit_count: currentCount + 1,
        last_used_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error incrementing edit count for magic link with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
      lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
    } as MagicLink;
  },

  async revoke(id: string): Promise<MagicLink> {
    const { data, error } = await supabase
      .from('magic_links')
      .update({ active: false })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error(`Error revoking magic link with id ${id}:`, error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      createdAt: new Date(converted.createdAt),
      expiresAt: converted.expiresAt ? new Date(converted.expiresAt) : null,
      lastUsedAt: converted.lastUsedAt ? new Date(converted.lastUsedAt) : null
    } as MagicLink;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('magic_links')
      .delete()
      .eq('id', id);

    if (error) {
      console.error(`Error deleting magic link with id ${id}:`, error);
      throw error;
    }
  }
};

// Access Logs Service
export const accessLogsService = {
  async getAll(): Promise<AccessLog[]> {
    const { data, error } = await supabase
      .from('access_logs')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching access logs:', error);
      throw error;
    }

    return data.map(log => {
      const converted = convertKeysToCamel(log);
      return {
        ...converted,
        timestamp: new Date(converted.timestamp),
        createdAt: new Date(converted.createdAt)
      } as AccessLog;
    });
  },

  async getById(id: string): Promise<AccessLog | null> {
    const { data, error } = await supabase
      .from('access_logs')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching access log with id ${id}:`, error);
      throw error;
    }

    if (!data) return null;

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      timestamp: new Date(converted.timestamp),
      createdAt: new Date(converted.createdAt)
    } as AccessLog;
  },

  async getByMagicLinkId(magicLinkId: string): Promise<AccessLog[]> {
    const { data, error } = await supabase
      .from('access_logs')
      .select('*')
      .eq('magic_link_id', magicLinkId)
      .order('timestamp', { ascending: false });

    if (error) {
      console.error(`Error fetching access logs for magic link ${magicLinkId}:`, error);
      throw error;
    }

    return data.map(log => {
      const converted = convertKeysToCamel(log);
      return {
        ...converted,
        timestamp: new Date(converted.timestamp),
        createdAt: new Date(converted.createdAt)
      } as AccessLog;
    });
  },

  async create(log: Omit<AccessLog, 'id' | 'timestamp' | 'createdAt'>): Promise<AccessLog> {
    const now = new Date();

    const logToInsert = {
      ...convertKeysToSnake(log),
      timestamp: now.toISOString()
    };

    const { data, error } = await supabase
      .from('access_logs')
      .insert(logToInsert)
      .select()
      .single();

    if (error) {
      console.error('Error creating access log:', error);
      throw error;
    }

    const converted = convertKeysToCamel(data);

    return {
      ...converted,
      timestamp: new Date(converted.timestamp),
      createdAt: new Date(converted.createdAt)
    } as AccessLog;
  }
};
