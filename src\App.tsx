import Layout from "@/components/Layout";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/contexts/AuthContext";
import { DataProvider } from "@/contexts/DataContext";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { SupabaseAuthProvider } from "@/contexts/SupabaseAuthContext";
import { SupabaseDataProvider } from "@/contexts/SupabaseDataContext";
import { SupabaseRealTimeProvider } from "@/contexts/SupabaseRealTimeContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import AccessRights from "./pages/AccessRights";
import AddYouth from "./pages/AddYouth";
import Assemblies from "./pages/Assemblies";
import Dashboard from "./pages/Dashboard";
import Districts from "./pages/Districts";
import Login from "./pages/Login";
import MagicAccess from "./pages/MagicAccess";
import NotFound from "./pages/NotFound";
import Permissions from "./pages/Permissions";
import Programs from "./pages/Programs";
import Regions from "./pages/Regions";
import Registration from "./pages/Registration";
import RegistrationError from "./pages/RegistrationError";
import RegistrationLinks from "./pages/RegistrationLinks";
import RegistrationSuccess from "./pages/RegistrationSuccess";
import Sermons from "./pages/Sermons";
import Settings from "./pages/Settings";
import SharedProgram from "./pages/SharedProgram";
import YouthDetails from "./pages/YouthDetails";
import YouthList from "./pages/YouthList";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <LanguageProvider>
        <SupabaseAuthProvider>
          <AuthProvider>
            <SupabaseRealTimeProvider>
              <SupabaseDataProvider>
                <DataProvider>
                  <Toaster />
                  <Sonner />
                  <BrowserRouter>
                    <Routes>
                      <Route path="/login" element={
                        <Layout requireAuth={false}>
                          <Login />
                        </Layout>
                      } />
                      {/* Registration route - matches the URL format in RegistrationLinks.tsx */}
                      <Route path="/register/:linkId" element={<Registration />} />
                      <Route path="/registration-success" element={<RegistrationSuccess />} />
                      <Route path="/registration-error" element={<RegistrationError />} />
                      <Route path="/program/share/:linkId" element={<SharedProgram />} />
                      <Route path="/access/:linkId" element={<MagicAccess />} />
                      <Route path="/" element={
                        <Layout>
                          <Dashboard />
                        </Layout>
                      } />
                      <Route path="/youths" element={
                        <Layout>
                          <YouthList />
                        </Layout>
                      } />
                      <Route path="/add-youth" element={
                        <Layout>
                          <AddYouth />
                        </Layout>
                      } />
                      <Route path="/youths/:youthId" element={
                        <Layout>
                          <YouthDetails />
                        </Layout>
                      } />
                      <Route path="/regions" element={
                        <Layout>
                          <Regions />
                        </Layout>
                      } />
                      <Route path="/districts" element={
                        <Layout>
                          <Districts />
                        </Layout>
                      } />
                      <Route path="/assemblies" element={
                        <Layout>
                          <Assemblies />
                        </Layout>
                      } />
                      <Route path="/permissions" element={
                        <Layout>
                          <Permissions />
                        </Layout>
                      } />
                      <Route path="/sermons" element={
                        <Layout>
                          <Sermons />
                        </Layout>
                      } />
                      <Route path="/programs" element={
                        <Layout>
                          <Programs />
                        </Layout>
                      } />
                      <Route path="/registration-links" element={
                        <Layout>
                          <RegistrationLinks />
                        </Layout>
                      } />
                      <Route path="/settings" element={
                        <Layout>
                          <Settings />
                        </Layout>
                      } />
                      <Route path="/access-rights" element={
                        <Layout>
                          <AccessRights />
                        </Layout>
                      } />

                      {/* Supabase routes have been merged with main routes */}
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </BrowserRouter>
                </DataProvider>
              </SupabaseDataProvider>
            </SupabaseRealTimeProvider>
          </AuthProvider>
        </SupabaseAuthProvider>
      </LanguageProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
