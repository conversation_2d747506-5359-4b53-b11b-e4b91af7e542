import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { initReactI18next } from 'react-i18next';

import translationEN from './locales/en/translation.json';
import translationFR from './locales/fr/translation.json';

// the translations
const resources = {
  en: {
    translation: translationEN
  },
  fr: {
    translation: translationFR
  }
};

i18n
  // detect user language
  .use(LanguageDetector)
  // pass the i18n instance to react-i18next
  .use(initReactI18next)
  // init i18next
  .init({
    resources,
    debug: false,

    // supported languages
    supportedLngs: ['en', 'fr'],

    // language to use if translations in user language are not available
    fallbackLng: 'fr',

    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },

    // common namespace used around the full app
    ns: ['translation'],
    defaultNS: 'translation',

    // language detection options
    detection: {
      order: ['querystring', 'cookie', 'localStorage', 'sessionStorage', 'navigator', 'htmlTag'],
      lookupQuerystring: 'lng',
      lookupCookie: 'i18next',
      lookupLocalStorage: 'language',
      lookupSessionStorage: 'i18nextLng',

      // cache user language
      caches: ['localStorage', 'cookie'],

      // optional expire and domain for cookies
      cookieExpirationDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 365), // 1 year
      cookieDomain: window.location.hostname,

      // only detect languages that are in the whitelist
      checkWhitelist: true
    }
  });

export default i18n;
