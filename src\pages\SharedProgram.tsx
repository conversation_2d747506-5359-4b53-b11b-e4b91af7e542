import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useData } from "@/contexts/DataContext";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { AlignmentType, Document, HeadingLevel, Packer, Paragraph, TextRun } from "docx";
import { saveAs } from "file-saver";
import { Clock, Download, Printer } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";

const SharedProgram = () => {
  const { linkId } = useParams<{ linkId: string }>();
  const {
    getProgramShareLinkById,
    getDailyProgramById,
    incrementProgramShareViewCount
  } = useData();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shareLink, setShareLink] = useState<any>(null);
  const [program, setProgram] = useState<any>(null);
  const programRef = useRef<HTMLDivElement>(null);
  const viewCountedRef = useRef<boolean>(false);

  useEffect(() => {
    console.log("SharedProgram component mounted with linkId:", linkId);

    if (!linkId) {
      console.error("No linkId provided");
      setError("Lien de partage invalide");
      setLoading(false);
      return;
    }

    // Vérifier si cette vue a déjà été comptée dans cette session
    const viewedLinks = localStorage.getItem('viewedProgramLinks') || '{}';
    let viewedLinksObj;
    try {
      viewedLinksObj = JSON.parse(viewedLinks);
    } catch (e) {
      viewedLinksObj = {};
    }

    // Récupérer les liens depuis le localStorage pour déboguer
    let parsedLinks = [];
    try {
      const savedLinks = localStorage.getItem('programShareLinks');
      if (savedLinks) {
        console.log("Raw programShareLinks from localStorage:", savedLinks);
        parsedLinks = JSON.parse(savedLinks);
        console.log("Parsed programShareLinks from localStorage:", parsedLinks);
      } else {
        console.warn("No programShareLinks found in localStorage");
      }
    } catch (e) {
      console.error("Error reading programShareLinks from localStorage:", e);
    }

    // Récupérer les programmes depuis le localStorage pour déboguer
    let parsedPrograms = [];
    try {
      const savedPrograms = localStorage.getItem('dailyPrograms');
      if (savedPrograms) {
        console.log("Raw dailyPrograms from localStorage:", savedPrograms);
        parsedPrograms = JSON.parse(savedPrograms);
        console.log("Parsed dailyPrograms from localStorage:", parsedPrograms);
      } else {
        console.warn("No dailyPrograms found in localStorage");
      }
    } catch (e) {
      console.error("Error reading dailyPrograms from localStorage:", e);
    }

    const link = getProgramShareLinkById(linkId);
    if (!link) {
      console.error(`Link with id ${linkId} not found`);
      console.log("Available links:", parsedLinks);
      setError("Lien de partage introuvable");
      setLoading(false);
      return;
    }

    console.log("Found share link:", link);
    setShareLink(link);

    // Incrémenter le compteur de vues seulement si ce n'est pas déjà fait dans cette session
    if (!viewCountedRef.current && !viewedLinksObj[linkId]) {
      console.log("Incrementing view count for the first time in this session");
      incrementProgramShareViewCount(linkId);
      viewCountedRef.current = true;

      // Marquer ce lien comme déjà vu dans cette session
      viewedLinksObj[linkId] = true;
      localStorage.setItem('viewedProgramLinks', JSON.stringify(viewedLinksObj));
    }

    const program = getDailyProgramById(link.programId);
    if (!program) {
      console.error(`Program with id ${link.programId} not found`);
      console.log("Available programs:", parsedPrograms);
      setError("Programme introuvable");
      setLoading(false);
      return;
    }

    console.log("Found program:", program);
    setProgram(program);
    setLoading(false);
  }, [linkId, getProgramShareLinkById, getDailyProgramById]);

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadWord = async () => {
    if (!program) return;

    // Create a new document
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: [
            new Paragraph({
              text: `Programme du ${format(new Date(program.date), "EEEE dd MMMM yyyy", { locale: fr })}`,
              heading: HeadingLevel.HEADING_1,
              alignment: AlignmentType.CENTER,
            }),
            new Paragraph({ text: "" }), // Empty paragraph for spacing
            ...program.activities
              .sort((a: any, b: any) => {
                if (a.startTime < b.startTime) return -1;
                if (a.startTime > b.startTime) return 1;
                return 0;
              })
              .flatMap((activity: any) => [
                new Paragraph({
                  children: [
                    new TextRun({
                      text: `${activity.startTime} - ${activity.endTime}: `,
                      bold: true,
                    }),
                    new TextRun({
                      text: activity.title,
                      bold: true,
                    }),
                  ],
                }),
                activity.responsiblePerson
                  ? new Paragraph({
                    text: `Responsable: ${activity.responsiblePerson}`,
                    indent: { left: 720 }, // 0.5 inch in twips
                  })
                  : new Paragraph({ text: "" }),
                activity.description
                  ? new Paragraph({
                    text: activity.description,
                    indent: { left: 720 }, // 0.5 inch in twips
                  })
                  : new Paragraph({ text: "" }),
                new Paragraph({ text: "" }), // Empty paragraph for spacing
              ]),
          ],
        },
      ],
    });

    // Generate and save the document
    const buffer = await Packer.toBlob(doc);
    saveAs(buffer, `programme_${format(new Date(program.date), "yyyy-MM-dd")}.docx`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    // Get more detailed debugging information
    let programShareLinksInfo = "Non";
    let dailyProgramsInfo = "Non";
    let programShareLinksCount = 0;
    let dailyProgramsCount = 0;

    try {
      const savedLinks = localStorage.getItem('programShareLinks');
      if (savedLinks) {
        const parsed = JSON.parse(savedLinks);
        programShareLinksInfo = "Oui";
        programShareLinksCount = parsed.length;
      }
    } catch (e) {
      console.error("Error parsing programShareLinks:", e);
    }

    try {
      const savedPrograms = localStorage.getItem('dailyPrograms');
      if (savedPrograms) {
        const parsed = JSON.parse(savedPrograms);
        dailyProgramsInfo = "Oui";
        dailyProgramsCount = parsed.length;
      }
    } catch (e) {
      console.error("Error parsing dailyPrograms:", e);
    }

    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold text-red-500 mb-4">{error}</h1>
        <p className="text-gray-500 mb-4">Le lien que vous avez suivi est invalide ou a expiré.</p>
        <div className="text-sm text-gray-400 mt-4 p-4 bg-gray-50 rounded-lg max-w-md">
          <p className="font-medium mb-2">Informations de débogage :</p>
          <p>ID du lien : {linkId || 'Non défini'}</p>
          <p>Liens de partage stockés : {programShareLinksInfo} ({programShareLinksCount} liens)</p>
          <p>Programmes stockés : {dailyProgramsInfo} ({dailyProgramsCount} programmes)</p>
        </div>
        <Button
          variant="outline"
          className="mt-6"
          onClick={() => window.location.href = '/programs'}
        >
          Retour à la page des programmes
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">Programme partagé</h1>
          <p className="text-gray-500">
            Visualisez le programme du{" "}
            {format(new Date(program.date), "EEEE dd MMMM yyyy", { locale: fr })}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            Imprimer
          </Button>
          <Button variant="outline" onClick={handleDownloadWord}>
            <Download className="h-4 w-4 mr-2" />
            Télécharger (.docx)
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            Programme du {format(new Date(program.date), "EEEE dd MMMM yyyy", { locale: fr })}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div ref={programRef} className="space-y-6">
            {program.activities
              .sort((a: any, b: any) => {
                if (a.startTime < b.startTime) return -1;
                if (a.startTime > b.startTime) return 1;
                return 0;
              })
              .map((activity: any) => (
                <div key={activity.id} className="grid grid-cols-12 gap-4">
                  <div className="col-span-3 md:col-span-2 flex flex-col">
                    <div className="flex items-center text-primary font-medium">
                      <Clock className="h-4 w-4 mr-1" />
                      {activity.startTime}
                    </div>
                    <div className="text-xs text-gray-500 ml-5">{activity.endTime}</div>
                  </div>
                  <div className="col-span-9 md:col-span-10">
                    <h3 className="font-medium">{activity.title}</h3>
                    {activity.responsiblePerson && (
                      <p className="text-sm text-gray-500">
                        Responsable: {activity.responsiblePerson}
                      </p>
                    )}
                    {activity.description && (
                      <div className="max-h-32 overflow-y-auto mt-1">
                        <p className="text-sm break-words whitespace-pre-wrap">{activity.description}</p>
                      </div>
                    )}
                  </div>
                  <div className="col-span-12">
                    <Separator className="mt-4" />
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>

      <div className="mt-6 text-center text-sm text-gray-500">
        <p>
          Ce programme est mis à jour en temps réel. Toute modification apportée par
          l'organisateur sera automatiquement reflétée ici.
        </p>
        <p className="mt-2">
          Vues: {shareLink.viewCount} | Créé le:{" "}
          {format(new Date(shareLink.createdAt), "dd/MM/yyyy à HH:mm", { locale: fr })}
        </p>
      </div>
    </div>
  );
};

export default SharedProgram;
