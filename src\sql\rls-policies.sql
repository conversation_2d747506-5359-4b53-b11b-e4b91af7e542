-- Enable Row Level Security on all tables
ALTER TABLE regions ENABLE ROW LEVEL SECURITY;
ALTER TABLE districts ENABLE ROW LEVEL SECURITY;
ALTER TABLE assemblies ENABLE ROW LEVEL SECURITY;
ALTER TABLE youths ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE sermons ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE program_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE program_share_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE access_rights ENABLE ROW LEVEL SECURITY;
ALTER TABLE magic_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE access_logs ENABLE ROW LEVEL SECURITY;

-- Create a function to check user role
CREATE OR REPLACE FUNCTION auth.user_role()
<PERSON><PERSON><PERSON><PERSON> TEXT AS $$
DECLARE
  user_id TEXT;
  user_role TEXT;
BEGIN
  -- Get the current user ID
  user_id := auth.uid();
  
  -- If no user is authenticated, return 'anonymous'
  IF user_id IS NULL THEN
    RETURN 'anonymous';
  END IF;
  
  -- Get the user's role from the users table
  SELECT role INTO user_role FROM users WHERE id = user_id;
  
  -- If the user doesn't have a role, return 'authenticated'
  IF user_role IS NULL THEN
    RETURN 'authenticated';
  END IF;
  
  RETURN user_role;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to check if a user has access to a feature
CREATE OR REPLACE FUNCTION auth.has_feature_access(feature_name TEXT, access_type TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_id TEXT;
  magic_link_id TEXT;
  access_right_id TEXT;
  has_access BOOLEAN;
  user_role TEXT;
BEGIN
  -- Get the current user ID
  user_id := auth.uid();
  
  -- If no user is authenticated, check if there's a magic link session
  IF user_id IS NULL THEN
    -- This would need to be implemented based on how you track magic link sessions
    -- For example, you might store the current magic link ID in a session variable
    -- or pass it as a header in API requests
    RETURN FALSE;
  END IF;
  
  -- Get the user's role
  user_role := auth.user_role();
  
  -- Super-admins have access to everything
  IF user_role = 'super-admin' THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user has access through a magic link
  SELECT ar.id INTO access_right_id
  FROM magic_links ml
  JOIN access_rights ar ON ml.access_right_id = ar.id
  WHERE ml.user_id = user_id
    AND ml.active = TRUE
    AND (ml.expires_at IS NULL OR ml.expires_at > NOW());
  
  IF access_right_id IS NOT NULL THEN
    -- Check if the access right includes the requested feature and access type
    SELECT EXISTS (
      SELECT 1
      FROM access_rights ar, jsonb_array_elements(ar.features) AS feature
      WHERE ar.id = access_right_id
        AND feature->>'feature' = feature_name
        AND (
          (access_type = 'read' AND (feature->>'access' = 'read' OR feature->>'access' = 'read-write'))
          OR
          (access_type = 'write' AND feature->>'access' = 'read-write')
        )
    ) INTO has_access;
    
    RETURN has_access;
  END IF;
  
  -- If we get here, the user doesn't have access
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create policies for regions table
CREATE POLICY "Allow read access to regions for authenticated users"
  ON regions FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('regions', 'read'));

CREATE POLICY "Allow insert/update access to regions for authorized users"
  ON regions FOR INSERT
  WITH CHECK (auth.role() = 'authenticated' AND auth.has_feature_access('regions', 'write'));

CREATE POLICY "Allow update access to regions for authorized users"
  ON regions FOR UPDATE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('regions', 'write'));

CREATE POLICY "Allow delete access to regions for authorized users"
  ON regions FOR DELETE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('regions', 'write'));

-- Create policies for districts table
CREATE POLICY "Allow read access to districts for authenticated users"
  ON districts FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('districts', 'read'));

CREATE POLICY "Allow insert access to districts for authorized users"
  ON districts FOR INSERT
  WITH CHECK (auth.role() = 'authenticated' AND auth.has_feature_access('districts', 'write'));

CREATE POLICY "Allow update access to districts for authorized users"
  ON districts FOR UPDATE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('districts', 'write'));

CREATE POLICY "Allow delete access to districts for authorized users"
  ON districts FOR DELETE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('districts', 'write'));

-- Create policies for assemblies table
CREATE POLICY "Allow read access to assemblies for authenticated users"
  ON assemblies FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('assemblies', 'read'));

CREATE POLICY "Allow insert access to assemblies for authorized users"
  ON assemblies FOR INSERT
  WITH CHECK (auth.role() = 'authenticated' AND auth.has_feature_access('assemblies', 'write'));

CREATE POLICY "Allow update access to assemblies for authorized users"
  ON assemblies FOR UPDATE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('assemblies', 'write'));

CREATE POLICY "Allow delete access to assemblies for authorized users"
  ON assemblies FOR DELETE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('assemblies', 'write'));

-- Create policies for youths table
CREATE POLICY "Allow read access to youths for authenticated users"
  ON youths FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('youths', 'read'));

CREATE POLICY "Allow insert access to youths for authorized users"
  ON youths FOR INSERT
  WITH CHECK (auth.role() = 'authenticated' AND auth.has_feature_access('youths', 'write'));

CREATE POLICY "Allow update access to youths for authorized users"
  ON youths FOR UPDATE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('youths', 'write'));

CREATE POLICY "Allow delete access to youths for authorized users"
  ON youths FOR DELETE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('youths', 'write'));

-- Create policies for payments table
CREATE POLICY "Allow read access to payments for authenticated users"
  ON payments FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('payments', 'read'));

CREATE POLICY "Allow insert access to payments for authorized users"
  ON payments FOR INSERT
  WITH CHECK (auth.role() = 'authenticated' AND auth.has_feature_access('payments', 'write'));

CREATE POLICY "Allow update access to payments for authorized users"
  ON payments FOR UPDATE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('payments', 'write'));

CREATE POLICY "Allow delete access to payments for authorized users"
  ON payments FOR DELETE
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('payments', 'write'));

-- Create policies for access_rights table (only super-admins can manage access rights)
CREATE POLICY "Allow read access to access_rights for super-admins"
  ON access_rights FOR SELECT
  USING (auth.user_role() = 'super-admin');

CREATE POLICY "Allow insert/update/delete access to access_rights for super-admins"
  ON access_rights FOR ALL
  USING (auth.user_role() = 'super-admin');

-- Create policies for magic_links table (only super-admins can manage magic links)
CREATE POLICY "Allow read access to magic_links for super-admins"
  ON magic_links FOR SELECT
  USING (auth.user_role() = 'super-admin');

CREATE POLICY "Allow insert/update/delete access to magic_links for super-admins"
  ON magic_links FOR ALL
  USING (auth.user_role() = 'super-admin');

-- Create policies for users table (users can only see and edit their own data)
CREATE POLICY "Users can view their own user data"
  ON users FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Super-admins can view all user data"
  ON users FOR SELECT
  USING (auth.user_role() = 'super-admin');

CREATE POLICY "Users can update their own user data"
  ON users FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Super-admins can update all user data"
  ON users FOR ALL
  USING (auth.user_role() = 'super-admin');
