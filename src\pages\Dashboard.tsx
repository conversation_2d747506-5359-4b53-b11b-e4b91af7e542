
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { useSupabaseData } from '@/contexts/SupabaseDataContext';
import { Bell, Calendar, FileText, Loader2, Map, RefreshCw, Users } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
  color?: string;
}

const StatCard = ({
  title,
  value,
  icon,
  description,
  trend,
  trendValue,
  color = 'bg-blue-500'
}: StatCardProps) => (
  <Card className="overflow-hidden">
    <CardHeader className="p-4 flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <div className={`w-8 h-8 ${color} rounded-lg flex items-center justify-center`}>
        {icon}
      </div>
    </CardHeader>
    <CardContent className="p-4 pt-0">
      <div className="text-2xl font-bold">{value}</div>
      {description && <CardDescription>{description}</CardDescription>}
      {trend && (
        <div className={`flex items-center text-xs ${trend === 'up' ? 'text-green-600' :
          trend === 'down' ? 'text-red-600' : 'text-gray-500'
          } mt-1`}>
          {trend === 'up' && '↑'}
          {trend === 'down' && '↓'}
          {trendValue}
        </div>
      )}
    </CardContent>
  </Card>
);

interface RegionalStatsProps {
  regionId: string;
  regionName: string;
}

const RegionalStats = ({ regionId, regionName }: RegionalStatsProps) => {
  const { getYouthsByRegion, getDistrictsByRegion } = useSupabaseData();
  const { t } = useTranslation();

  const youths = getYouthsByRegion(regionId);
  const districts = getDistrictsByRegion(regionId);
  const maleCount = youths.filter(y => y.gender === 'male').length;
  const femaleCount = youths.filter(y => y.gender === 'female').length;

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-md">{regionName}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold text-primary">{youths.length}</div>
            <div className="text-sm text-gray-500">{t('common.total')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600">{maleCount}</div>
            <div className="text-sm text-gray-500">{t('common.male')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-pink-600">{femaleCount}</div>
            <div className="text-sm text-gray-500">{t('common.female')}</div>
          </div>
        </div>
        <div className="mt-4 text-sm">
          <span className="font-medium">{districts.length}</span> {t('dashboard.districts')}
        </div>
      </CardContent>
    </Card>
  );
};

const Dashboard = () => {
  const {
    youths,
    regions,
    permissions,
    sermons,
    dailyPrograms,
    refreshData,
    isLoading
  } = useSupabaseData();
  const { toast } = useToast();
  const { t } = useTranslation();

  const totalMale = youths.filter(y => y.gender === 'male').length;
  const totalFemale = youths.filter(y => y.gender === 'female').length;
  const pendingPermissions = permissions.filter(p => !p.approved).length;

  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const todayProgram = dailyPrograms.find(p => {
    const programDate = new Date(p.date);
    programDate.setHours(0, 0, 0, 0);
    return programDate.getTime() === today.getTime();
  });

  const handleRefreshData = async () => {
    try {
      await refreshData();
      toast({
        title: t('common.success'),
        description: t('common.dataRefreshed'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('common.errorRefreshingData'),
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('dashboard.title')}</h1>
          <p className="text-gray-500">{t('dashboard.welcome')}</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefreshData}
          className="flex items-center gap-1"
          disabled={isLoading.youths || isLoading.regions || isLoading.permissions || isLoading.sermons || isLoading.dailyPrograms}
        >
          {isLoading.youths || isLoading.regions || isLoading.permissions || isLoading.sermons || isLoading.dailyPrograms ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          {t('common.refresh')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title={t('dashboard.totalYouths')}
          value={youths.length.toString()}
          icon={<Users className="h-4 w-4 text-white" />}
          color="bg-primary"
        />

        <StatCard
          title={t('dashboard.totalRegions')}
          value={regions.length.toString()}
          icon={<Map className="h-4 w-4 text-white" />}
          color="bg-secondary"
        />

        <StatCard
          title={t('dashboard.pendingPermissions')}
          value={pendingPermissions.toString()}
          icon={<Bell className="h-4 w-4 text-white" />}
          color="bg-orange-500"
        />

        <StatCard
          title={t('dashboard.sermonsRecorded')}
          value={sermons.length.toString()}
          icon={<FileText className="h-4 w-4 text-white" />}
          color="bg-accent"
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>{t('dashboard.youthDistribution')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 flex items-center justify-center text-gray-400">
              {youths.length === 0 ? (
                <div className="text-center">
                  <p>{t('common.noData')}</p>
                  <p className="text-sm mt-2">{t('dashboard.addYouthsToSeeStats')}</p>
                </div>
              ) : (
                <div className="w-full space-y-4">
                  <div className="flex justify-between">
                    <span>{t('common.male')}</span>
                    <span>{totalMale} ({youths.length > 0 ? Math.round((totalMale / youths.length) * 100) : 0}%)</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-blue-600 h-2.5 rounded-full" style={{
                      width: `${youths.length > 0 ? (totalMale / youths.length) * 100 : 0}%`
                    }}></div>
                  </div>

                  <div className="flex justify-between">
                    <span>{t('common.female')}</span>
                    <span>{totalFemale} ({youths.length > 0 ? Math.round((totalFemale / youths.length) * 100) : 0}%)</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2.5">
                    <div className="bg-pink-500 h-2.5 rounded-full" style={{
                      width: `${youths.length > 0 ? (totalFemale / youths.length) * 100 : 0}%`
                    }}></div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t('dashboard.todayProgram')}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 overflow-auto">
              {todayProgram ? (
                <div className="space-y-3">
                  {todayProgram.activities.map((activity, index) => (
                    <div key={index} className="p-3 border rounded-md">
                      <div className="font-medium">{activity.title}</div>
                      <div className="text-sm text-gray-500">
                        {activity.startTime} - {activity.endTime}
                      </div>
                      {activity.responsiblePerson && (
                        <div className="text-xs text-gray-400 mt-1">
                          {t('dashboard.responsible')}: {activity.responsiblePerson}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <p>{t('dashboard.noProgramToday')}</p>
                    <p className="text-sm mt-2">{t('dashboard.addActivitiesInProgramSection')}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div>
        <h2 className="text-xl font-semibold mb-4">{t('dashboard.statisticsByRegion')}</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {regions.map((region) => (
            <RegionalStats
              key={region.id}
              regionId={region.id}
              regionName={region.name}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
