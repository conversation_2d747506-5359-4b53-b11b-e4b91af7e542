import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { useSupabaseData } from '@/contexts/SupabaseDataContext';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, CreditCard, Edit, MapPin, Plus, User } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';


// Add print styles
const printStyles = `
  @media print {
    body * {
      visibility: hidden;
    }
    .print-section, .print-section * {
      visibility: visible;
    }
    .print-section {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }
    .no-print {
      display: none !important;
    }
  }
`;

const YouthDetails = () => {
  const { youthId } = useParams<{ youthId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();


  const {
    getYouthById,
    getAssemblyById,
    getDistrictById,
    getRegionById,
    updateYouth,
    regions,
    districts,
    assemblies,
    getDistrictsByRegion,
    getAssembliesByDistrict,
    payments,
    addPayment
  } = useSupabaseData();

  const [loading, setLoading] = useState(true);
  const [youth, setYouth] = useState<any>(null);
  const [assembly, setAssembly] = useState<any>(null);
  const [district, setDistrict] = useState<any>(null);
  const [region, setRegion] = useState<any>(null);
  const [youthPayments, setYouthPayments] = useState<any[]>([]);

  // Edit dialog state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editFirstName, setEditFirstName] = useState('');
  const [editLastName, setEditLastName] = useState('');
  const [editGender, setEditGender] = useState('');
  const [editRegionId, setEditRegionId] = useState('');
  const [editDistrictId, setEditDistrictId] = useState('');
  const [editAssemblyId, setEditAssemblyId] = useState('');
  const [filteredDistricts, setFilteredDistricts] = useState(districts);
  const [filteredAssemblies, setFilteredAssemblies] = useState(assemblies);

  // Payment dialog state
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('cash');
  const [paymentReference, setPaymentReference] = useState('');
  const [paymentDate, setPaymentDate] = useState('');

  // Handle printing
  const handlePrint = () => {
    if (!youth) return;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
      toast({
        title: t('common.error'),
        description: "Could not open print window. Please check your popup settings.",
        variant: 'destructive',
      });
      return;
    }

    // Get assembly, district, and region information
    const assembly = youth.assembly;
    const district = youth.district;
    const region = youth.region;

    // Generate the HTML content for printing
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${t('youths.youthDetails')}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { text-align: center; margin-bottom: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .section { margin-bottom: 20px; }
            .section-title { font-weight: bold; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
            .info-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
            .info-item { margin-bottom: 10px; }
            .info-label { font-weight: bold; }
            .footer { text-align: center; margin-top: 30px; font-size: 12px; color: #666; }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>${t('youths.youthDetails')}</h1>
            <p>${t('common.printDate')}: ${format(new Date(), 'dd/MM/yyyy')}</p>
          </div>

          <div class="section">
            <div class="section-title">${t('youths.personalInfo')}</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">${t('common.firstName')}:</div>
                <div>${youth.firstName}</div>
              </div>
              <div class="info-item">
                <div class="info-label">${t('common.lastName')}:</div>
                <div>${youth.lastName}</div>
              </div>
              <div class="info-item">
                <div class="info-label">${t('common.gender')}:</div>
                <div>${youth.gender === 'male' ? t('common.male') : t('common.female')}</div>
              </div>
              <div class="info-item">
                <div class="info-label">${t('common.registrationDate')}:</div>
                <div>${format(new Date(youth.registrationDate), 'dd/MM/yyyy')}</div>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="section-title">${t('youths.churchInfo')}</div>
            <div class="info-grid">
              <div class="info-item">
                <div class="info-label">${t('common.region')}:</div>
                <div>${region?.name || t('common.unknown')}</div>
              </div>
              <div class="info-item">
                <div class="info-label">${t('common.district')}:</div>
                <div>${district?.name || t('common.unknown')}</div>
              </div>
              <div class="info-item">
                <div class="info-label">${t('common.assembly')}:</div>
                <div>${assembly?.name || t('common.unknown')}</div>
              </div>
            </div>
          </div>

          <div class="footer">
            <p>${t('youths.printedOn')} ${format(new Date(), 'dd/MM/yyyy')}</p>
          </div>
        </body>
      </html>
    `;

    // Write the content to the new window
    printWindow.document.write(printContent);
    printWindow.document.close();

    // Wait for the content to load before printing
    printWindow.onload = function () {
      printWindow.print();
      // Close the window after printing (optional)
      // printWindow.close();
    };

    toast({
      title: t('common.success'),
      description: t('common.printSuccess'),
    });
  };

  useEffect(() => {
    if (!youthId) {
      navigate('/youths');
      return;
    }

    const foundYouth = getYouthById(youthId);
    if (!foundYouth) {
      navigate('/youths');
      return;
    }

    setYouth(foundYouth);

    // Get assembly, district, and region information
    const foundAssembly = getAssemblyById(foundYouth.assemblyId);
    setAssembly(foundAssembly);

    let foundDistrict = null;
    let foundRegion = null;

    if (foundAssembly) {
      foundDistrict = getDistrictById(foundAssembly.districtId);
      setDistrict(foundDistrict);

      if (foundDistrict) {
        foundRegion = getRegionById(foundDistrict.regionId);
        setRegion(foundRegion);
      }
    }

    // Get payments for this youth
    const foundPayments = payments.filter(p => p.youthId === youthId);
    setYouthPayments(foundPayments);

    setLoading(false);

    // Initialiser les valeurs d'édition
    setEditFirstName(foundYouth.firstName);
    setEditLastName(foundYouth.lastName);
    setEditGender(foundYouth.gender);
    setEditAssemblyId(foundYouth.assemblyId);

    if (foundAssembly) {
      setEditDistrictId(foundAssembly.districtId);

      if (foundDistrict) {
        setEditRegionId(foundDistrict.regionId);
        setFilteredDistricts(getDistrictsByRegion(foundDistrict.regionId));
        setFilteredAssemblies(getAssembliesByDistrict(foundAssembly.districtId));
      }
    }

    // Initialiser la date de paiement à aujourd'hui
    const today = new Date();
    const formattedDate = today.toISOString().split('T')[0];
    setPaymentDate(formattedDate);

    // Check if we should print automatically
    if (searchParams.get('print') === 'true') {
      // Use setTimeout to ensure the component is fully rendered
      setTimeout(() => {
        window.print();
      }, 500);
    }

  }, [youthId, getYouthById, getAssemblyById, getDistrictById, getRegionById, navigate, payments, getDistrictsByRegion, getAssembliesByDistrict, searchParams]);

  const handleBack = () => {
    navigate('/youths');
  };

  // Handle region change
  const handleRegionChange = (value: string) => {
    setEditRegionId(value);
    setEditDistrictId('');
    setEditAssemblyId('');

    if (value) {
      const districtsInRegion = getDistrictsByRegion(value);
      setFilteredDistricts(districtsInRegion);
      setFilteredAssemblies([]);
    } else {
      setFilteredDistricts([]);
      setFilteredAssemblies([]);
    }
  };

  // Handle district change
  const handleDistrictChange = (value: string) => {
    setEditDistrictId(value);
    setEditAssemblyId('');

    if (value) {
      const assembliesInDistrict = getAssembliesByDistrict(value);
      setFilteredAssemblies(assembliesInDistrict);
    } else {
      setFilteredAssemblies([]);
    }
  };

  // Open edit dialog
  const handleOpenEditDialog = () => {
    setIsEditDialogOpen(true);
  };

  // Save edit changes
  const handleSaveEdit = () => {
    if (!editFirstName || !editLastName || !editGender || !editAssemblyId) {
      toast({
        title: t('common.error'),
        description: t('youths.fillAllRequiredFields'),
        variant: "destructive"
      });
      return;
    }

    updateYouth(youthId!, {
      firstName: editFirstName,
      lastName: editLastName,
      gender: editGender,
      assemblyId: editAssemblyId
    });

    // Update local data
    setYouth({
      ...youth,
      firstName: editFirstName,
      lastName: editLastName,
      gender: editGender,
      assemblyId: editAssemblyId
    });

    // Update assembly, district and region
    const updatedAssembly = getAssemblyById(editAssemblyId);
    setAssembly(updatedAssembly);

    if (updatedAssembly) {
      const updatedDistrict = getDistrictById(updatedAssembly.districtId);
      setDistrict(updatedDistrict);

      if (updatedDistrict) {
        const updatedRegion = getRegionById(updatedDistrict.regionId);
        setRegion(updatedRegion);
      }
    }

    setIsEditDialogOpen(false);

    toast({
      title: t('common.success'),
      description: t('youths.detailsUpdated')
    });
  };

  // Ouvrir le modal d'ajout de paiement
  const handleOpenPaymentDialog = () => {
    setIsPaymentDialogOpen(true);
  };

  // Add payment
  const handleAddPayment = () => {
    if (!paymentAmount || !paymentMethod || !paymentDate) {
      toast({
        title: t('common.error'),
        description: t('youths.fillAllRequiredFields'),
        variant: "destructive"
      });
      return;
    }

    const amount = parseFloat(paymentAmount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: t('common.error'),
        description: t('payments.amountMustBePositive'),
        variant: "destructive"
      });
      return;
    }

    const newPayment = {
      youthId: youthId!,
      amount,
      method: paymentMethod,
      reference: paymentReference,
      date: new Date(paymentDate)
    };

    addPayment(newPayment);

    // Update payments list
    const updatedPayment = {
      ...newPayment,
      id: 'temp-id' // Will be replaced with real ID
    };
    setYouthPayments([...youthPayments, updatedPayment]);

    // Reset form
    setPaymentAmount('');
    setPaymentReference('');

    setIsPaymentDialogOpen(false);

    toast({
      title: t('common.success'),
      description: t('payments.paymentAddedSuccess')
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!youth) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-2">{t('youths.notFound')}</h2>
        <p className="text-gray-500 mb-6">{t('youths.notFoundDescription')}</p>
        <Button onClick={handleBack}>{t('common.back')}</Button>
      </div>
    );
  }

  // Calculate total payments
  const totalPayments = youthPayments.reduce((sum, payment) => sum + payment.amount, 0);

  return (
    <div className="space-y-6">
      <style>{printStyles}</style>
      <div className="flex items-center justify-between no-print">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{t('youths.details')}</h1>
            <p className="text-gray-500">{t('youths.detailsDescription')}</p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleOpenEditDialog} className="flex items-center gap-2">
            <Edit className="h-4 w-4" />
            {t('common.edit')}
          </Button>
        </div>
      </div>

      <div className="print-section">
        <h1 className="text-2xl font-bold text-center mb-6 hidden print:block">{t('youths.details')}</h1>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {t('youths.personalInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-500">{t('youths.firstName')}</h3>
                  <p className="text-lg">{youth.firstName}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-500">{t('youths.lastName')}</h3>
                  <p className="text-lg">{youth.lastName}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">{t('youths.gender')}</h3>
                <p className="text-lg">{youth.gender === 'male' ? t('common.male') : t('common.female')}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">{t('youths.registeredOn')}</h3>
                <p className="text-lg flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  {format(new Date(youth.registrationDate), 'dd/MM/yyyy')}
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                {t('youths.churchInfo')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="text-sm font-medium text-gray-500">{t('common.assembly')}</h3>
                <p className="text-lg">{assembly?.name || t('common.notSpecified')}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">{t('common.district')}</h3>
                <p className="text-lg">{district?.name || t('common.notSpecified')}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-gray-500">{t('common.region')}</h3>
                <p className="text-lg">{region?.name || t('common.notSpecified')}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Card className="no-print">
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              {t('youths.paymentHistory')}
            </CardTitle>
            <CardDescription>
              {t('payments.paymentHistoryDescription')}
            </CardDescription>
          </div>
          <Button size="sm" onClick={handleOpenPaymentDialog} className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            {t('common.add')}
          </Button>
        </CardHeader>
        <CardContent>
          {youthPayments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>{t('payments.noPayments')}</p>
            </div>
          ) : (
            <div className="space-y-4">
              {youthPayments.map((payment) => (
                <div key={payment.id} className="p-4 border rounded-lg">
                  <div className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{payment.amount} FCFA</p>
                      <p className="text-sm text-gray-500">
                        {format(new Date(payment.date), 'dd/MM/yyyy')}
                      </p>
                    </div>
                    <div className="text-right">
                      <span className="inline-block px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                        {payment.method === 'cash' ? t('payments.cash') :
                          payment.method === 'mobile_money' ? t('payments.mobileMoney') :
                            payment.method === 'bank_transfer' ? t('payments.bankTransfer') : t('payments.other')}
                      </span>
                      {payment.reference && (
                        <p className="text-xs text-gray-500 mt-1">{t('payments.ref')}: {payment.reference}</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
        <CardFooter className="border-t bg-gray-50 flex justify-between">
          <span className="font-medium">{t('payments.totalPayments')}</span>
          <span className="font-bold">{totalPayments} FCFA</span>
        </CardFooter>
      </Card>

      <div className="flex justify-end no-print">
        <Button variant="outline" onClick={handleBack}>
          {t('common.back')}
        </Button>
      </div>

      {/* Edit dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('youths.editDetails')}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4 flex-1 overflow-y-auto pr-2">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">{t('youths.firstName')}</Label>
                <Input
                  id="firstName"
                  value={editFirstName}
                  onChange={(e) => setEditFirstName(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">{t('youths.lastName')}</Label>
                <Input
                  id="lastName"
                  value={editLastName}
                  onChange={(e) => setEditLastName(e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="gender">{t('youths.gender')}</Label>
              <Select value={editGender} onValueChange={setEditGender}>
                <SelectTrigger id="gender">
                  <SelectValue placeholder={t('youths.selectGender')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="male">{t('common.male')}</SelectItem>
                  <SelectItem value="female">{t('common.female')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="region">{t('common.region')}</Label>
              <Select value={editRegionId} onValueChange={handleRegionChange}>
                <SelectTrigger id="region">
                  <SelectValue placeholder={t('youths.selectRegion')} />
                </SelectTrigger>
                <SelectContent>
                  {regions.map((region) => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="district">{t('common.district')}</Label>
              <Select
                value={editDistrictId}
                onValueChange={handleDistrictChange}
                disabled={!editRegionId}
              >
                <SelectTrigger id="district">
                  <SelectValue placeholder={t('youths.selectDistrict')} />
                </SelectTrigger>
                <SelectContent>
                  {filteredDistricts.map((district) => (
                    <SelectItem key={district.id} value={district.id}>
                      {district.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="assembly">{t('common.assembly')}</Label>
              <Select
                value={editAssemblyId}
                onValueChange={setEditAssemblyId}
                disabled={!editDistrictId}
              >
                <SelectTrigger id="assembly">
                  <SelectValue placeholder={t('youths.selectAssembly')} />
                </SelectTrigger>
                <SelectContent>
                  {filteredAssemblies.map((assembly) => (
                    <SelectItem key={assembly.id} value={assembly.id}>
                      {assembly.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleSaveEdit}>{t('common.save')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Payment dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="sm:max-w-[425px]" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('payments.addPayment')}</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="amount">{t('payments.amount')} (FCFA)</Label>
              <Input
                id="amount"
                type="number"
                value={paymentAmount}
                onChange={(e) => setPaymentAmount(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="method">{t('payments.method')}</Label>
              <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                <SelectTrigger id="method">
                  <SelectValue placeholder={t('payments.selectMethod')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">{t('payments.cash')}</SelectItem>
                  <SelectItem value="mobile_money">{t('payments.mobileMoney')}</SelectItem>
                  <SelectItem value="bank_transfer">{t('payments.bankTransfer')}</SelectItem>
                  <SelectItem value="other">{t('payments.other')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="reference">{t('payments.reference')} ({t('common.optional')})</Label>
              <Input
                id="reference"
                value={paymentReference}
                onChange={(e) => setPaymentReference(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">{t('payments.date')}</Label>
              <Input
                id="date"
                type="date"
                value={paymentDate}
                onChange={(e) => setPaymentDate(e.target.value)}
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsPaymentDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleAddPayment}>{t('common.add')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default YouthDetails;