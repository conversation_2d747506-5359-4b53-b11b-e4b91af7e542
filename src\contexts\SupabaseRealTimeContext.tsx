import { supabase } from '@/lib/supabase';
import React, { createContext, useContext, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

interface SupabaseRealTimeContextType {
  isRealTimeEnabled: boolean;
  enableRealTime: () => void;
  disableRealTime: () => void;
}

const SupabaseRealTimeContext = createContext<SupabaseRealTimeContextType | undefined>(undefined);

export const SupabaseRealTimeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = useQueryClient();
  const [isRealTimeEnabled, setIsRealTimeEnabled] = React.useState(true);

  useEffect(() => {
    if (!isRealTimeEnabled) return;

    // Set up real-time subscriptions for all tables
    const regionsSubscription = supabase
      .channel('regions-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'regions',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['regions'] });
      })
      .subscribe();

    const districtsSubscription = supabase
      .channel('districts-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'districts',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['districts'] });
      })
      .subscribe();

    const assembliesSubscription = supabase
      .channel('assemblies-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'assemblies',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['assemblies'] });
      })
      .subscribe();

    const youthsSubscription = supabase
      .channel('youths-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'youths',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['youths'] });
      })
      .subscribe();

    const paymentsSubscription = supabase
      .channel('payments-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'payments',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['payments'] });
      })
      .subscribe();

    const permissionsSubscription = supabase
      .channel('permissions-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'permissions',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['permissions'] });
      })
      .subscribe();

    const sermonsSubscription = supabase
      .channel('sermons-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'sermons',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['sermons'] });
      })
      .subscribe();

    const dailyProgramsSubscription = supabase
      .channel('daily-programs-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'daily_programs',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['dailyPrograms'] });
      })
      .subscribe();

    const programActivitiesSubscription = supabase
      .channel('program-activities-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'program_activities',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['dailyPrograms'] });
      })
      .subscribe();

    const accessRightsSubscription = supabase
      .channel('access-rights-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'access_rights',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['accessRights'] });
      })
      .subscribe();

    const magicLinksSubscription = supabase
      .channel('magic-links-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'magic_links',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['magicLinks'] });
      })
      .subscribe();

    const accessLogsSubscription = supabase
      .channel('access-logs-changes')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'access_logs',
      }, () => {
        queryClient.invalidateQueries({ queryKey: ['accessLogs'] });
      })
      .subscribe();

    // Clean up subscriptions when component unmounts
    return () => {
      regionsSubscription.unsubscribe();
      districtsSubscription.unsubscribe();
      assembliesSubscription.unsubscribe();
      youthsSubscription.unsubscribe();
      paymentsSubscription.unsubscribe();
      permissionsSubscription.unsubscribe();
      sermonsSubscription.unsubscribe();
      dailyProgramsSubscription.unsubscribe();
      programActivitiesSubscription.unsubscribe();
      accessRightsSubscription.unsubscribe();
      magicLinksSubscription.unsubscribe();
      accessLogsSubscription.unsubscribe();
    };
  }, [queryClient, isRealTimeEnabled]);

  const enableRealTime = () => {
    setIsRealTimeEnabled(true);
  };

  const disableRealTime = () => {
    setIsRealTimeEnabled(false);
  };

  return (
    <SupabaseRealTimeContext.Provider value={{
      isRealTimeEnabled,
      enableRealTime,
      disableRealTime
    }}>
      {children}
    </SupabaseRealTimeContext.Provider>
  );
};

export const useSupabaseRealTime = () => {
  const context = useContext(SupabaseRealTimeContext);
  if (context === undefined) {
    throw new Error('useSupabaseRealTime must be used within a SupabaseRealTimeProvider');
  }
  return context;
};
