
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { useData } from "@/contexts/DataContext";
import { format } from "date-fns";
import { FileText, Printer, Search, UserPlus } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const YouthsList = () => {
  const {
    youths,
    regions,
    districts,
    assemblies,
    getRegionById,
    getDistrictById,
    getAssemblyById,
    getDistrictsByRegion,
    getAssembliesByDistrict
  } = useData();

  const navigate = useNavigate();
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [selectedAssembly, setSelectedAssembly] = useState("");
  const [filteredDistricts, setFilteredDistricts] = useState(districts);
  const [filteredAssemblies, setFilteredAssemblies] = useState(assemblies);

  // Filtrage des districts en fonction de la région sélectionnée
  const handleRegionChange = (value: string) => {
    setSelectedRegion(value);
    setSelectedDistrict("");
    setSelectedAssembly("");

    if (value && value !== "all") {
      const districtsInRegion = getDistrictsByRegion(value);
      setFilteredDistricts(districtsInRegion);
      setFilteredAssemblies([]);
    } else {
      setFilteredDistricts(districts);
      setFilteredAssemblies(assemblies);
    }
  };

  // Filtrage des assemblées en fonction du district sélectionné
  const handleDistrictChange = (value: string) => {
    setSelectedDistrict(value);
    setSelectedAssembly("");

    if (value && value !== "all") {
      const assembliesInDistrict = getAssembliesByDistrict(value);
      setFilteredAssemblies(assembliesInDistrict);
    } else {
      setFilteredAssemblies([]);
    }
  };

  // Filtrer les jeunes en fonction des critères
  const filteredYouths = youths.filter((youth) => {
    // Filtrer par texte de recherche
    const matchesSearch =
      youth.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      youth.lastName.toLowerCase().includes(searchTerm.toLowerCase());

    // Filtrer par assemblée si sélectionnée
    const matchesAssembly = selectedAssembly && selectedAssembly !== "all"
      ? youth.assemblyId === selectedAssembly
      : true;

    // Filtrer par district si sélectionné
    const matchesDistrict = selectedDistrict && selectedDistrict !== "all"
      ? getAssemblyById(youth.assemblyId)?.districtId === selectedDistrict
      : true;

    // Filtrer par région si sélectionnée
    const matchesRegion = selectedRegion && selectedRegion !== "all"
      ? getDistrictById(getAssemblyById(youth.assemblyId)?.districtId || "")?.regionId === selectedRegion
      : true;

    return matchesSearch && matchesAssembly && matchesDistrict && matchesRegion;
  });

  // Navigate to add youth page
  const handleAddYouth = () => {
    navigate("/add-youth");
  };

  // Fonction pour imprimer la liste
  const handlePrint = () => {
    const printContent = document.getElementById("print-content");
    const windowUrl = "about:blank";
    const uniqueName = new Date().getTime();
    const windowName = "Print" + uniqueName;

    if (printContent) {
      const printWindow = window.open(windowUrl, windowName, "left=0,top=0,width=800,height=900,toolbar=0,scrollbars=1,status=0");

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${t('youths.title')} - ${t('app.title')}</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { text-align: center; margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .filters { margin-bottom: 20px; }
              </style>
            </head>
            <body>
              <h1>${t('youths.title')} - ${t('app.title')}</h1>
              <div class="filters">
                ${selectedRegion ? `<p><strong>${t('sidebar.regions')}:</strong> ${getRegionById(selectedRegion)?.name || ''}</p>` : ''}
                ${selectedDistrict ? `<p><strong>${t('sidebar.districts')}:</strong> ${getDistrictById(selectedDistrict)?.name || ''}</p>` : ''}
                ${selectedAssembly ? `<p><strong>${t('sidebar.assemblies')}:</strong> ${getAssemblyById(selectedAssembly)?.name || ''}</p>` : ''}
              </div>
              <table>
                <thead>
                  <tr>
                    <th>${t('common.lastName')}</th>
                    <th>${t('common.firstName')}</th>
                    <th>${t('common.gender')}</th>
                    <th>${t('sidebar.assemblies')}</th>
                    <th>${t('sidebar.districts')}</th>
                    <th>${t('sidebar.regions')}</th>
                    <th>${t('youths.registrationDate')}</th>
                  </tr>
                </thead>
                <tbody>
                  ${filteredYouths.map(youth => {
          const assembly = getAssemblyById(youth.assemblyId);
          const district = assembly ? getDistrictById(assembly.districtId) : null;
          const region = district ? getRegionById(district.regionId) : null;

          return `
                      <tr>
                        <td>${youth.lastName}</td>
                        <td>${youth.firstName}</td>
                        <td>${youth.gender === 'male' ? t('common.male') : t('common.female')}</td>
                        <td>${assembly?.name || ''}</td>
                        <td>${district?.name || ''}</td>
                        <td>${region?.name || ''}</td>
                        <td>${format(new Date(youth.registrationDate), 'dd/MM/yyyy')}</td>
                      </tr>
                    `;
        }).join('')}
                </tbody>
              </table>
              <div style="text-align: center; margin-top: 30px;">
                <p>${t('youths.totalCount')}: ${filteredYouths.length}</p>
                <p>${t('youths.printedOn')} ${format(new Date(), 'dd/MM/yyyy à HH:mm')}</p>
              </div>
            </body>
          </html>
        `);

        printWindow.document.close();
        printWindow.focus();

        // Attendre que le contenu soit chargé avant d'imprimer
        printWindow.onload = function () {
          printWindow.print();
          printWindow.close();
        };
      }
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('youths.title')}</h1>
          <p className="text-gray-500">{t('youths.manageParticipants') || "Gérez la liste des participants au camp"}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handlePrint}>
            <Printer className="h-4 w-4 mr-2" />
            {t('common.print')}
          </Button>
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            {t('common.export') || "Exporter"}
          </Button>
          <Button onClick={handleAddYouth}>
            <UserPlus className="h-4 w-4 mr-2" />
            {t('youths.addYouth')}
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div className="relative lg:col-span-2">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder={t('common.searchByName')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>

            <div>
              <Select value={selectedRegion} onValueChange={handleRegionChange}>
                <SelectTrigger>
                  <SelectValue placeholder={t('sidebar.regions')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('youths.allRegions') || "Toutes les régions"}</SelectItem>
                  {regions.map((region) => (
                    <SelectItem key={region.id} value={region.id}>
                      {region.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select
                value={selectedDistrict}
                onValueChange={handleDistrictChange}
                disabled={!selectedRegion || selectedRegion === "all"}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('sidebar.districts')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('youths.allDistricts') || "Tous les districts"}</SelectItem>
                  {filteredDistricts.map((district) => (
                    <SelectItem key={district.id} value={district.id}>
                      {district.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Select
                value={selectedAssembly}
                onValueChange={setSelectedAssembly}
                disabled={!selectedDistrict || selectedDistrict === "all"}
              >
                <SelectTrigger>
                  <SelectValue placeholder={t('sidebar.assemblies')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t('youths.allAssemblies') || "Toutes les assemblées"}</SelectItem>
                  {filteredAssemblies.map((assembly) => (
                    <SelectItem key={assembly.id} value={assembly.id}>
                      {assembly.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      <div id="print-content">
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.lastName')}</TableHead>
                    <TableHead>{t('common.firstName')}</TableHead>
                    <TableHead>{t('common.gender')}</TableHead>
                    <TableHead>{t('sidebar.assemblies')}</TableHead>
                    <TableHead>{t('sidebar.districts')}</TableHead>
                    <TableHead>{t('sidebar.regions')}</TableHead>
                    <TableHead>{t('youths.registrationDate') || "Date d'inscription"}</TableHead>
                    <TableHead className="w-20">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredYouths.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                        {t('youths.noYouths')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredYouths.map((youth) => {
                      const assembly = getAssemblyById(youth.assemblyId);
                      const district = assembly ? getDistrictById(assembly.districtId) : null;
                      const region = district ? getRegionById(district.regionId) : null;

                      return (
                        <TableRow key={youth.id}>
                          <TableCell>{youth.lastName}</TableCell>
                          <TableCell>{youth.firstName}</TableCell>
                          <TableCell>
                            {youth.gender === 'male' ? t('common.male') : t('common.female')}
                          </TableCell>
                          <TableCell>{assembly?.name}</TableCell>
                          <TableCell>{district?.name}</TableCell>
                          <TableCell>{region?.name}</TableCell>
                          <TableCell>
                            {format(new Date(youth.registrationDate), 'dd/MM/yyyy')}
                          </TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/youths/${youth.id}`)}
                            >
                              {t('common.details')}
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-4 text-sm text-gray-500">
        {t('common.total')}: {filteredYouths.length} {filteredYouths.length > 1 ? t('youths.youthsPlural') || "jeunes" : t('youths.youthSingular') || "jeune"}
      </div>
    </div>
  );
};

export default YouthsList;
