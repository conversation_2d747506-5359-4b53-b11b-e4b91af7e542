{"app": {"title": "Camp des jeunes", "subtitle": "Youth Management"}, "sidebar": {"collapse": "Collapse menu", "expand": "Expand menu", "dashboard": "Dashboard", "participants": "Participants", "youthList": "Youth List", "registrationLinks": "Registration Links", "organization": "Organization", "regions": "Regions", "districts": "Districts", "assemblies": "Assemblies", "permissions": "Permissions", "sermons": "<PERSON><PERSON>", "programs": "Programs", "settings": "Settings", "accessRights": "Access Rights", "logout": "Logout"}, "common": {"search": "Search...", "searchByName": "Search by first or last name...", "add": "Add", "edit": "Edit", "delete": "Delete", "cancel": "Cancel", "save": "Save", "create": "Create", "actions": "Actions", "details": "Details", "view": "View", "name": "Name", "firstName": "First Name", "lastName": "Last Name", "age": "Age", "gender": "Gender", "male": "Male", "female": "Female", "total": "Total", "email": "Email", "phone": "Phone", "address": "Address", "date": "Date", "startDate": "Start Date", "endDate": "End Date", "status": "Status", "active": "Active", "inactive": "Inactive", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "description": "Description", "notes": "Notes", "submit": "Submit", "confirm": "Confirm", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "loading": "Loading...", "noData": "No data available", "all": "All", "none": "None", "yes": "Yes", "no": "No", "back": "Back", "next": "Next", "previous": "Previous", "copy": "Copy", "copied": "<PERSON>pied", "share": "Share", "print": "Print", "printDate": "Print Date", "printSuccess": "Print successful", "download": "Download", "upload": "Upload", "export": "Export", "language": "Language", "french": "French", "english": "English", "browserLanguage": "Browser Language", "languageDetected": "Language Detected", "usingBrowserLanguage": "Using your browser language: {{language}}", "region": "Region", "district": "District", "assembly": "Assembly", "location": "Location", "unknown": "Unknown", "notSpecified": "Not specified", "optional": "Optional", "refresh": "Refresh", "dataRefreshed": "Data refreshed", "errorRefreshingData": "Error refreshing data", "noSearchResults": "No search results", "registrationDate": "Registration Date", "id": "ID", "showing": "Showing", "of": "of", "clearFilters": "Clear Filters", "tryAgain": "Try Again", "link": "Link"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "loginButton": "<PERSON><PERSON>", "accessDenied": "Access Denied", "accessDeniedMessage": "You don't have the necessary permissions to access this page", "loginDescription": "Enter your credentials to access the platform", "usernamePlaceholder": "Your username", "passwordPlaceholder": "Your password", "fillAllFields": "Please fill in all fields", "incorrectCredentials": "Incorrect credentials", "contactAdmin": "To get an account, please contact the administrator"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to the youth camp management", "resetData": "Reset data", "confirmClearData": "Are you sure you want to clear all data? This action is irreversible.", "dataCleared": "Data cleared", "dataClearedSuccess": "All data has been successfully cleared.", "totalYouths": "Total Youths", "totalRegions": "Total Regions", "totalDistricts": "Total Districts", "totalAssemblies": "Total Assemblies", "recentRegistrations": "Recent Registrations", "upcomingPrograms": "Upcoming Programs", "pendingPermissions": "Pending Permissions", "sermonsRecorded": "<PERSON><PERSON> Recorded", "youthDistribution": "Youth Distribution", "addYouthsToSeeStats": "Add youths to see statistics", "todayProgram": "Today's Program", "responsible": "Responsible", "noProgramToday": "No program for today", "addActivitiesInProgramSection": "Add activities in the Program section", "statisticsByRegion": "Statistics by Region", "districts": "district(s)", "filteringByRegion": "Filtering by region", "filteringByDistrict": "Filtering by district"}, "youths": {"title": "Youth List", "addYouth": "Add Youth", "editYouth": "Edit Youth", "deleteYouth": "Delete Youth", "youthDetails": "Youth Details", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "churchInfo": "Church Information", "paymentHistory": "Payment History", "permissionHistory": "Permission History", "noYouths": "No youths found", "manageParticipants": "Manage camp participants list", "allRegions": "All regions", "allDistricts": "All districts", "allAssemblies": "All assemblies", "registrationDate": "Registration date", "totalCount": "Total count", "printedOn": "Printed on", "youthSingular": "youth", "youthsPlural": "youths", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "assemblyRequired": "Assembly is required", "addedSuccessfully": "Youth has been added successfully", "errorAdding": "An error occurred while adding the youth", "selectRegion": "Select a region", "selectDistrict": "Select a district", "selectAssembly": "Select an assembly", "searchPlaceholder": "Search by name...", "subtitle": "View and manage youth participants", "filters": "Filters", "noYouthsFound": "No youths found", "tryDifferentFilters": "Try different filters or clear the search", "youths": "youths", "youthList": "Youth List", "errorLoadingDetails": "Error loading youth details", "detailsUpdated": "Youth details updated successfully", "updateError": "Error updating youth details", "fillAllRequiredFields": "Please fill in all required fields", "notFound": "Youth Not Found", "notFoundDescription": "The requested youth could not be found", "details": "Youth Details", "detailsDescription": "View and manage youth information", "registeredOn": "Registered on", "editDetails": "Edit Youth Details", "firstName": "First Name", "lastName": "Last Name", "gender": "Gender", "selectGender": "Select gender", "printAll": "Print all youths", "printFiltered": "Print filtered list"}, "accessRights": {"title": "Access Rights Management", "subtitle": "Manage access rights and magic links", "accessRights": "Access Rights", "accessRightsList": "List of configured access rights", "magicLinks": "Magic Links", "magicLinksList": "List of generated access links", "accessLogs": "Access Logs", "accessLogsDescription": "History of accesses via magic links", "newAccessRight": "New Access Right", "editAccessRight": "Edit Access Right", "deleteAccessRight": "Delete Access Right", "features": "Features", "accessType": "Access Type", "access": "Access", "none": "None", "read": "Read", "write": "Write", "readWrite": "Read and Write", "createMagicLink": "Create Magic Link", "newMagicLink": "New Magic Link", "createLinkDescription": "Create a new magic link to share access to specific features", "magicLinkName": "Link Name", "magicLinkNamePlaceholder": "Enter a name for this magic link", "expirationDate": "Expiration Date", "creationDate": "Creation Date", "activeLinks": "Active Links", "accessRight": "Access Right", "selectAccessRight": "Select an access right", "noExpiration": "No expiration", "link": "Link", "revoke": "Revoke", "revoked": "Revoked", "views": "Views", "edits": "Edits", "accessDate": "Access Date", "action": "Action", "feature": "Feature", "details": "Details", "fillAllFields": "Please fill in all required fields", "loginRequired": "You must be logged in to create an access right", "rightCreated": "The access right has been created successfully", "rightCreateError": "An error occurred while creating the access right", "rightDeleted": "The access right has been deleted successfully", "rightDeleteError": "An error occurred while deleting the access right", "linkCreated": "The magic link has been created successfully", "linkCreateError": "An error occurred while creating the magic link", "linkRevoked": "The magic link has been revoked successfully", "linkRevokeError": "An error occurred while revoking the magic link", "linkDeleted": "The magic link has been deleted successfully", "linkDeleteError": "An error occurred while deleting the magic link", "linkCopied": "The link has been copied to the clipboard", "linkCopyError": "Unable to copy the link", "noAccessRights": "No access rights configured", "noMagicLinks": "No magic links generated", "featureLabels": {"youths": "Participants", "payments": "Payments", "permissions": "Permissions", "programs": "Programs", "sermons": "<PERSON><PERSON>", "regions": "Regions", "districts": "Districts", "assemblies": "Assemblies"}}, "sermons": {"title": "<PERSON><PERSON>", "subtitle": "Camp sermon archives", "newSermon": "New Sermon", "editSermon": "<PERSON>", "sermonList": "Sermon List", "preacher": "<PERSON><PERSON>", "theme": "Theme", "date": "Date", "references": "Biblical References", "content": "Content", "actions": "Actions", "noSermons": "No sermons recorded", "collapse": "Collapse", "expand": "View", "modify": "Edit", "print": "Print", "download": "Download (.docx)", "preacherPlaceholder": "<PERSON><PERSON>'s name", "themePlaceholder": "Sermon theme", "referencesPlaceholder": "<PERSON> 3:16; Romans 8:28", "referencesHelp": "Separate references with semicolons (;)", "contentPlaceholder": "Sermon content...", "by": "By", "printedOn": "Printed on", "preparingDownload": "Preparing download", "generatingDocument": "Generating Word document...", "downloadSuccess": "The sermon was downloaded successfully", "downloadError": "An error occurred while generating the document", "fillAllFields": "Please fill in all fields", "updateSuccess": "The sermon has been updated successfully", "addSuccess": "The sermon has been added successfully", "operationError": "An error occurred during the operation", "printError": "Unable to open print window"}, "programs": {"title": "Title", "subtitle": "Camp activity planning", "newProgram": "New Daily Program", "editProgram": "Edit Program", "programFor": "Program for", "noPrograms": "No programs defined", "noProgramsSubtitle": "Create your first daily program for the camp", "createProgram": "Create program", "activities": "Activities", "activity": "Activity", "addActivity": "Add Activity", "removeActivity": "Remove Activity", "atLeastOneActivity": "You must have at least one activity", "description": "Description", "startTime": "Start Time", "endTime": "End Time", "responsible": "Responsible Person", "modify": "Edit", "print": "Print", "download": "Download (.docx)", "share": "Share", "copyLink": "Copy Link", "linkCopied": "Link copied to clipboard", "shareError": "An error occurred while creating the share link", "activityTitleRequired": "Activity title is required", "startTimeRequired": "Start time is required", "endTimeRequired": "End time is required", "dateRequired": "Date is required", "programAddSuccess": "The program has been added successfully", "programUpdateSuccess": "The program has been updated successfully", "operationError": "An error occurred during the operation", "dragToReorder": "Drag to reorder activities", "todayProgram": "Today's program", "activitiesReordered": "Activities reordered", "activitiesReorderedSuccess": "The order of activities has been updated", "testLink": "Test link", "noShareLink": "No share link found. Please create a share link first.", "fillAllFields": "Please fill in all required fields for each activity", "programDate": "Program date", "activityNumber": "Activity", "activityTitle": "Title*", "activityTitlePlaceholder": "Activity title", "responsiblePlaceholder": "Responsible person's name", "descriptionPlaceholder": "Activity description", "saveChanges": "Save changes", "activeLinks": "Active Links", "registrationLinksList": "List of all generated registration links", "noRegistrationLinks": "No registration links", "clickCreateLinkHint": "Click on \"Create Link\" to generate a new registration link", "createLink": "Create Link", "newLinkCreated": "New Link Created", "shareLinkWithYouths": "Share this link with youths who want to register", "manageRegistrationLinks": "Manage registration links for youths", "registrations": "Registrations", "linkCreated": "The registration link has been created successfully", "linkDeleted": "The registration link has been deleted successfully", "testLinkDeleteError": "Test links cannot be deleted", "confirmDeleteLink": "Are you sure you want to delete this registration link?"}, "permissions": {"title": "Permission Requests", "subtitle": "Manage youth permission requests", "newPermission": "New Permission", "permissionRequests": "Permission Requests", "reason": "Reason", "period": "Period", "status": "Status", "approved": "Approved", "pending": "Pending", "approve": "Approve", "noPermissions": "No permission requests", "selectYouth": "Select a youth", "reasonPlaceholder": "Reason for permission", "permissionApproved": "Permission approved", "permissionApprovedSuccess": "The permission has been approved successfully", "permissionAddSuccess": "The permission has been added successfully", "operationError": "An error occurred during the operation", "fillAllFields": "Please fill in all fields"}, "settings": {"title": "Settings", "profile": "Profile", "notifications": "Notifications", "security": "Security", "notificationPreferences": "Notification Preferences", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Receive emails for important information", "pushNotifications": "Push Notifications", "pushNotificationsDescription": "Receive push notifications for important information", "notificationsUpdated": "Notification settings updated", "notificationsUpdatedDescription": "Your notification preferences have been updated.", "securitySettings": "Security Settings", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm Password", "passwordChanged": "Password changed", "passwordChangedDescription": "Your password has been changed successfully.", "passwordError": "Password error", "passwordErrorDescription": "An error occurred while changing your password.", "passwordMismatch": "Passwords do not match", "passwordRequired": "Password is required", "profileSettings": "Profile Settings", "name": "Name", "email": "Email", "username": "Username", "fullName": "Full Name", "updateProfile": "Update Profile", "profileUpdated": "Profile updated", "profileUpdatedDescription": "Your profile has been updated successfully.", "profileError": "Profile error", "profileErrorDescription": "An error occurred while updating your profile."}, "regions": {"title": "Region Management", "addRegion": "Add Region", "regionList": "Region List", "regionName": "Region Name", "regionNamePlaceholder": "Region name", "regionNameRequired": "Region name is required", "regionExists": "This region already exists", "regionAdded": "Region added", "regionAddedSuccess": "The region has been added successfully", "regionAddError": "An error occurred while adding the region", "noRegions": "No regions available", "details": "Details", "regionDetails": "Region Details", "actions": "Actions"}, "districts": {"title": "District Management", "addDistrict": "Add District", "districtList": "District List", "districtName": "District Name", "districtNamePlaceholder": "District name", "selectRegion": "Select a region", "districtNameRequired": "District name is required", "regionRequired": "Region is required", "districtExists": "This district already exists in the selected region", "districtAdded": "District added", "districtAddedSuccess": "The district has been added successfully", "districtAddError": "An error occurred while adding the district", "noDistricts": "No districts available", "details": "Details", "districtDetails": "District Details", "actions": "Actions", "region": "Region"}, "assemblies": {"title": "Assembly Management", "addAssembly": "Add Assembly", "assemblyList": "Assembly List", "assemblyName": "Assembly Name", "assemblyNamePlaceholder": "Assembly name", "selectDistrict": "Select a district", "assemblyNameRequired": "Assembly name is required", "districtRequired": "District is required", "assemblyExists": "This assembly already exists in the selected district", "assemblyAdded": "Assembly added", "assemblyAddedSuccess": "The assembly has been added successfully", "assemblyAddError": "An error occurred while adding the assembly", "noAssemblies": "No assemblies available", "details": "Details", "assemblyDetails": "Assembly Details", "actions": "Actions", "district": "District"}, "notFound": {"title": "404", "message": "Oops! Page not found", "returnHome": "Return to Home"}, "payments": {"title": "Payments", "description": "Payment history for this youth", "add": "Add Payment", "addPayment": "Add Payment", "amount": "Amount", "method": "Payment Method", "date": "Payment Date", "reference": "Reference", "referencePlaceholder": "Transaction reference or note", "methodCash": "Cash", "methodMobileMoney": "Mobile Money", "methodBankTransfer": "Bank Transfer", "methodOther": "Other", "selectMethod": "Select payment method", "noPayments": "No payments recorded", "totalPaid": "Total Paid", "numberOfPayments": "Number of Payments", "lastPayment": "Last Payment", "summary": "Payment Summary", "paymentAdded": "Payment Added", "paymentAddedSuccess": "The payment has been added successfully", "paymentAddError": "An error occurred while adding the payment", "fillAllRequiredFields": "Please fill in all required fields", "invalidAmount": "Please enter a valid amount greater than zero", "addError": "Error adding payment", "cash": "Cash", "mobileMoney": "Mobile Money", "bankTransfer": "Bank Transfer", "other": "Other", "ref": "Ref", "totalPayments": "Total Payments", "paymentHistoryDescription": "Payment history for this participant", "amountMustBePositive": "Amount must be a positive number"}, "registration": {"form": "Registration Form", "title": "Camp Registration", "description": "Please fill out this form to register for the youth camp.", "submit": "Register", "submitting": "Registering...", "contactInfo": "For any questions, please contact the camp organizer.", "checkingLink": "Verifying registration link...", "invalidLink": {"title": "Invalid Link", "description": "This registration link is not valid or has expired."}, "expiredLink": {"title": "Expired Link", "description": "This registration link has expired."}, "maxReached": {"title": "Maximum Reached", "description": "The maximum number of registrations for this link has been reached."}, "formError": {"title": "Form Error", "description": "Please fill in all required fields."}, "error": {"title": "Invalid Registration Link", "message": "The registration link you used is invalid, has expired, or has reached its usage limit.", "description": "An error occurred during registration. Please try again.", "whatToDo": "What to do?", "checkLink": "Check that you used the correct link", "requestNewLink": "Request a new registration link from the organizer", "contactOrganizer": "Contact the organizer to register directly", "closePage": "Close this page"}, "success": {"title": "Registration Successful!", "message": "Your registration for the youth camp has been successfully recorded. We look forward to welcoming you!", "whatNext": "What to do now?", "waitConfirmation": "Wait for confirmation of your registration by the organizer", "prepareCamp": "Prepare for the camp (dates, things to bring, etc.)", "contactOrganizer": "Feel free to contact the organizer with any questions", "closePage": "Close this page"}}}