
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { useAuth } from '@/contexts/AuthContext';
import {
  Book,
  Calendar,
  ChevronDown,
  ChevronRight,
  Clock,
  FileText,
  Home,
  Key,
  LogOut,
  Map,
  Settings,
  Users
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';

const NavItem = ({
  to,
  label,
  icon: Icon,
  isActive = false,
  onClick
}: {
  to: string;
  label: string;
  icon: React.ElementType;
  isActive?: boolean;
  onClick?: () => void;
}) => (
  <Link
    to={to}
    onClick={onClick}
    className={`flex items-center p-3 rounded-md transition-all ${isActive ? 'bg-primary/10 text-primary font-medium' : 'text-gray-600 hover:bg-gray-100'
      }`}
  >
    <Icon size={18} className="mr-3" />
    <span>{label}</span>
  </Link>
);

interface SubNavProps {
  title: string;
  icon: React.ElementType;
  isOpen: boolean;
  toggleOpen: () => void;
  isActive: boolean;
  children: React.ReactNode;
}

const SubNav = ({ title, icon: Icon, isOpen, toggleOpen, isActive, children }: SubNavProps) => (
  <div className="mb-1">
    <button
      onClick={toggleOpen}
      className={`flex items-center justify-between w-full p-3 rounded-md transition-all ${isActive ? 'bg-primary/10 text-primary font-medium' : 'text-gray-600 hover:bg-gray-100'
        }`}
    >
      <div className="flex items-center">
        <Icon size={18} className="mr-3" />
        <span>{title}</span>
      </div>
      {isOpen ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
    </button>
    {isOpen && <div className="pl-10 mt-1 space-y-1">{children}</div>}
  </div>
);

const Sidebar = () => {
  const location = useLocation();
  const { logout, user } = useAuth();
  const { t } = useTranslation();
  const [openMenus, setOpenMenus] = useState<Record<string, boolean>>({});
  const [collapsed, setCollapsed] = useState(false);

  // Récupérer les informations d'authentification
  const { isMagicLinkSession } = useAuth();

  // Initialiser la marge du contenu principal
  useEffect(() => {
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.style.marginLeft = collapsed ? '5rem' : '16rem';
    }
  }, [collapsed]);

  // Vérifier si l'utilisateur est un super-admin et n'est pas connecté via un lien magique
  const isSuperAdmin = user?.role === 'super-admin' && !isMagicLinkSession;

  const toggleMenu = (menu: string) => {
    setOpenMenus(prev => ({ ...prev, [menu]: !prev[menu] }));
  };

  const toggleSidebar = () => {
    const newCollapsedState = !collapsed;
    setCollapsed(newCollapsedState);

    // Ajuster la marge du contenu principal
    const mainContent = document.getElementById('main-content');
    if (mainContent) {
      mainContent.style.marginLeft = newCollapsedState ? '5rem' : '16rem';
    }
  };

  const isActive = (path: string) => location.pathname === path;
  const isMenuActive = (paths: string[]) => paths.some(path => location.pathname.startsWith(path));

  const handleLogout = () => {
    logout();
  };

  return (
    <div className={`${collapsed ? 'w-20' : 'w-64'} bg-white border-r h-screen fixed top-0 left-0 flex flex-col transition-all duration-300 z-50`}>
      <div className="p-4 border-b flex justify-between items-center">
        {!collapsed ? (
          <>
            <div>
              <h1 className="text-xl font-bold text-primary">{t('app.title')}</h1>
              <p className="text-sm text-gray-500">{t('app.subtitle')}</p>
            </div>
            <button
              onClick={toggleSidebar}
              className="text-gray-500 hover:text-primary transition-colors"
              title={t('sidebar.collapse')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                <path d="M9 3v18" />
                <path d="m16 15-3-3 3-3" />
              </svg>
            </button>
          </>
        ) : (
          <div className="w-full flex justify-center">
            <button
              onClick={toggleSidebar}
              className="text-gray-500 hover:text-primary transition-colors"
              title={t('sidebar.expand')}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
                <path d="M9 3v18" />
                <path d="m13 15 3-3-3-3" />
              </svg>
            </button>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-2">
        {collapsed ? (
          // Version réduite du menu
          <div className="flex flex-col items-center space-y-4">
            <Link to="/" className={`p-2 rounded-md ${isActive('/') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.dashboard')}>
              <Home size={20} />
            </Link>
            <Link to="/youths" className={`p-2 rounded-md ${isActive('/youths') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.youthList')}>
              <Users size={20} />
            </Link>
            <Link to="/regions" className={`p-2 rounded-md ${isActive('/regions') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.regions')}>
              <Map size={20} />
            </Link>
            <Link to="/permissions" className={`p-2 rounded-md ${isActive('/permissions') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.permissions')}>
              <Clock size={20} />
            </Link>
            <Link to="/sermons" className={`p-2 rounded-md ${isActive('/sermons') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.sermons')}>
              <Book size={20} />
            </Link>
            <Link to="/programs" className={`p-2 rounded-md ${isActive('/programs') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.programs')}>
              <Calendar size={20} />
            </Link>
            <Link to="/settings" className={`p-2 rounded-md ${isActive('/settings') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.settings')}>
              <Settings size={20} />
            </Link>


            {isSuperAdmin && (
              <Link to="/access-rights" className={`p-2 rounded-md ${isActive('/access-rights') ? 'bg-primary/10 text-primary' : 'text-gray-600 hover:bg-gray-100'}`} title={t('sidebar.accessRights')}>
                <Key size={20} />
              </Link>
            )}
          </div>
        ) : (
          // Version complète du menu
          <>
            <NavItem to="/" label={t('sidebar.dashboard')} icon={Home} isActive={isActive('/')} />

            <SubNav
              title={t('sidebar.participants')}
              icon={Users}
              isOpen={!!openMenus.participants}
              toggleOpen={() => toggleMenu('participants')}
              isActive={isMenuActive(['/youths', '/registration-links'])}
            >
              <NavItem to="/youths" label={t('sidebar.youthList')} icon={Users} isActive={isActive('/youths')} />
              <NavItem to="/registration-links" label={t('sidebar.registrationLinks')} icon={FileText} isActive={isActive('/registration-links')} />
            </SubNav>

            <SubNav
              title={t('sidebar.organization')}
              icon={Map}
              isOpen={!!openMenus.organization}
              toggleOpen={() => toggleMenu('organization')}
              isActive={isMenuActive(['/regions', '/districts', '/assemblies'])}
            >
              <NavItem to="/regions" label={t('sidebar.regions')} icon={Map} isActive={isActive('/regions')} />
              <NavItem to="/districts" label={t('sidebar.districts')} icon={Map} isActive={isActive('/districts')} />
              <NavItem to="/assemblies" label={t('sidebar.assemblies')} icon={Map} isActive={isActive('/assemblies')} />
            </SubNav>

            <NavItem to="/permissions" label={t('sidebar.permissions')} icon={Clock} isActive={isActive('/permissions')} />
            <NavItem to="/sermons" label={t('sidebar.sermons')} icon={Book} isActive={isActive('/sermons')} />
            <NavItem to="/programs" label={t('sidebar.programs')} icon={Calendar} isActive={isActive('/programs')} />
            <NavItem to="/settings" label={t('sidebar.settings')} icon={Settings} isActive={isActive('/settings')} />


            {isSuperAdmin && (
              <NavItem to="/access-rights" label={t('sidebar.accessRights')} icon={Key} isActive={isActive('/access-rights')} />
            )}
          </>
        )}
      </div>

      <div className="p-4 border-t flex justify-between items-center">
        <button
          onClick={handleLogout}
          className={`${collapsed ? 'justify-center' : ''} flex items-center text-gray-600 hover:text-red-600 transition-colors`}
          title={t('sidebar.logout')}
        >
          <LogOut size={18} className={collapsed ? '' : 'mr-3'} />
          {!collapsed && <span>{t('sidebar.logout')}</span>}
        </button>
        {collapsed ? (
          <LanguageSwitcher showLabel={false} />
        ) : (
          <LanguageSwitcher showLabel={true} />
        )}
      </div>
    </div>
  );
};

export default Sidebar;
