
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { Globe, MapPin, PlusCircle, RefreshCw, Users } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

const Regions = () => {
  const {
    regions,
    addRegion,
    isLoading,
    refreshData,
    getDistrictsByRegion,
    getYouthsByRegion
  } = useSupabaseData();

  const { t } = useTranslation();
  const [newRegionName, setNewRegionName] = useState("");
  const [error, setError] = useState("");

  // State for region details dialog
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedRegionId, setSelectedRegionId] = useState<string | null>(null);

  const handleAddRegion = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newRegionName.trim()) {
      setError(t('regions.regionNameRequired'));
      return;
    }

    // Check if region already exists
    if (regions.some(region => region.name.toLowerCase() === newRegionName.trim().toLowerCase())) {
      setError(t('regions.regionExists'));
      return;
    }

    try {
      addRegion(newRegionName.trim());
      toast({
        title: t('regions.regionAdded'),
        description: t('regions.regionAddedSuccess'),
      });
      setNewRegionName("");
      setError("");
    } catch (error) {
      console.error("Error adding region:", error);
      toast({
        title: t('common.error'),
        description: t('regions.regionAddError'),
        variant: "destructive",
      });
    }
  };

  const handleShowDetails = (regionId: string) => {
    setSelectedRegionId(regionId);
    setIsDetailsDialogOpen(true);
  };

  // Get the selected region object
  const selectedRegion = selectedRegionId
    ? regions.find(region => region.id === selectedRegionId)
    : null;

  // Get districts for the selected region
  const regionDistricts = selectedRegionId
    ? getDistrictsByRegion(selectedRegionId)
    : [];

  // Get youths for the selected region
  const regionYouths = selectedRegionId
    ? getYouthsByRegion(selectedRegionId)
    : [];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('regions.title')}</h1>
        <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.regions}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.regions ? 'animate-spin' : ''}`} />
          {t('common.refresh')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('regions.addRegion')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddRegion} className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder={t('regions.regionNamePlaceholder')}
                  value={newRegionName}
                  onChange={(e) => {
                    setNewRegionName(e.target.value);
                    setError("");
                  }}
                  className={error ? "border-destructive" : ""}
                />
                {error && <p className="text-sm text-destructive">{error}</p>}
              </div>
              <Button type="submit" className="w-full">
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('common.add')}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('regions.regionList')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.name')}</TableHead>
                    <TableHead className="w-24">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {regions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={2} className="text-center py-4 text-gray-500">
                        {t('regions.noRegions')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    regions.map((region) => (
                      <TableRow key={region.id}>
                        <TableCell>{region.name}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShowDetails(region.id)}
                          >
                            {t('regions.details')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Region Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('regions.regionDetails')}</DialogTitle>
          </DialogHeader>

          {selectedRegion && (
            <div className="space-y-6 py-4 overflow-y-auto">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">{selectedRegion.name}</h3>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-primary" />
                  <h3 className="text-md font-medium">{t('districts.title')}</h3>
                </div>
                {regionDistricts.length === 0 ? (
                  <p className="text-sm text-gray-500">{t('districts.noDistricts')}</p>
                ) : (
                  <ul className="space-y-1 pl-7 list-disc text-sm">
                    {regionDistricts.map(district => (
                      <li key={district.id}>{district.name}</li>
                    ))}
                  </ul>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  <h3 className="text-md font-medium">{t('youths.title')}</h3>
                </div>
                <p className="text-sm">
                  {t('common.total')}: <span className="font-medium">{regionYouths.length}</span>
                </p>
                <div className="flex gap-4 text-sm">
                  <p>
                    {t('common.male')}: <span className="font-medium">
                      {regionYouths.filter(youth => youth.gender === 'male').length}
                    </span>
                  </p>
                  <p>
                    {t('common.female')}: <span className="font-medium">
                      {regionYouths.filter(youth => youth.gender === 'female').length}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Regions;
