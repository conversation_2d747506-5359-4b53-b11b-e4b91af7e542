
import LanguageDetectionNotification from '@/components/LanguageDetectionNotification';
import Sidebar from '@/components/Sidebar';
import { useAuth } from '@/contexts/AuthContext';
import { useData } from '@/contexts/DataContext';
import { toast } from '@/hooks/use-toast';
import { FeatureType } from '@/types';
import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

// Mapping des chemins d'URL aux fonctionnalités
const pathToFeatureMap: Record<string, FeatureType> = {
  '/youths': 'youths',
  '/add-youth': 'youths',
  '/payments': 'payments',
  '/permissions': 'permissions',
  '/programs': 'programs',
  '/sermons': 'sermons',
  '/regions': 'regions',
  '/districts': 'districts',
  '/assemblies': 'assemblies',
};

interface LayoutProps {
  children: React.ReactNode;
  requireAuth?: boolean;
}

const Layout = ({ children, requireAuth = true }: LayoutProps) => {
  const { isAuthenticated, isLoading, user, currentMagicLink, isMagicLinkSession } = useAuth();
  const { checkAccess, addAccessLog } = useData();
  const location = useLocation();

  // Déterminer la fonctionnalité actuelle en fonction du chemin
  const getCurrentFeature = (): FeatureType | null => {
    const path = location.pathname;

    // Vérifier les correspondances exactes
    if (pathToFeatureMap[path]) {
      return pathToFeatureMap[path];
    }

    // Vérifier les correspondances partielles
    for (const [urlPath, feature] of Object.entries(pathToFeatureMap)) {
      if (path.startsWith(urlPath)) {
        return feature;
      }
    }

    return null;
  };

  // Vérifier si l'utilisateur a accès à la page actuelle
  const checkPageAccess = () => {
    // Si l'utilisateur est connecté normalement (pas via un lien magique), il a accès à tout
    if (user) {
      // Vérifier si l'utilisateur est un super-admin pour la page des droits d'accès
      if (location.pathname === '/access-rights' && user.role !== 'super-admin') {
        toast({
          title: "Accès refusé",
          description: "Vous n'avez pas les droits nécessaires pour accéder à cette page",
          variant: "destructive",
        });
        return false;
      }
      return true;
    }

    // Si l'utilisateur est connecté via un lien magique, vérifier ses droits
    if (isMagicLinkSession && currentMagicLink) {
      // Bloquer l'accès à la page des droits d'accès pour tous les utilisateurs connectés via un lien magique
      if (location.pathname === '/access-rights') {
        toast({
          title: "Accès refusé",
          description: "Seul un super-administrateur peut accéder à cette page",
          variant: "destructive",
        });
        return false;
      }

      const currentFeature = getCurrentFeature();

      // Si la page n'est pas associée à une fonctionnalité, autoriser l'accès (ex: dashboard)
      if (!currentFeature) {
        return true;
      }

      // Vérifier les droits d'accès en lecture
      const hasAccess = checkAccess(currentMagicLink.id, currentFeature, 'read');

      if (hasAccess) {
        // Enregistrer l'accès dans les logs
        addAccessLog({
          magicLinkId: currentMagicLink.id,
          action: 'view',
          feature: currentFeature,
          details: `Accès à la page ${location.pathname}`
        });
        return true;
      } else {
        toast({
          title: "Accès refusé",
          description: "Vous n'avez pas les droits nécessaires pour accéder à cette page",
          variant: "destructive",
        });
        return false;
      }
    }

    return false;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" />;
  }

  if (!requireAuth && isAuthenticated) {
    return <Navigate to="/" />;
  }

  if (requireAuth) {
    // Vérifier l'accès à la page actuelle
    if (location.pathname !== '/login' && !checkPageAccess()) {
      return <Navigate to="/" />;
    }

    return (
      <div className="flex min-h-screen bg-gray-50">
        <LanguageDetectionNotification />
        <Sidebar />
        <div className="flex-1 overflow-auto ml-64 transition-all duration-300" id="main-content">
          <main className="p-6">{children}</main>
        </div>
      </div>
    );
  }

  // For non-authenticated pages (login, register)
  return (
    <div className="min-h-screen bg-gray-50">
      <LanguageDetectionNotification />
      {children}
    </div>
  );
};

export default Layout;
