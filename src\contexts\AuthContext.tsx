
import { User } from '@/types';
import React, { createContext, useContext, useEffect, useState } from 'react';

interface MagicLinkSession {
  id: string;
  accessRightId: string;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  currentMagicLink: MagicLinkSession | null;
  isMagicLinkSession: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentMagicLink, setCurrentMagicLink] = useState<MagicLinkSession | null>(null);

  // Simuler la vérification de l'authentification lors du chargement de l'application
  useEffect(() => {
    const checkAuth = () => {
      // Vérifier d'abord l'authentification normale
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          setUser(JSON.parse(storedUser));
        } catch (error) {
          console.error('Failed to parse stored user:', error);
          localStorage.removeItem('user');
        }
      }

      // Vérifier ensuite s'il y a une session par lien magique
      const storedMagicLink = localStorage.getItem('currentMagicLink');
      if (storedMagicLink) {
        try {
          setCurrentMagicLink(JSON.parse(storedMagicLink));
        } catch (error) {
          console.error('Failed to parse stored magic link:', error);
          localStorage.removeItem('currentMagicLink');
        }
      }

      setIsLoading(false);
    };

    checkAuth();
  }, []);

  const login = async (username: string, password: string) => {
    // Pour la démo, nous allons simplement accepter n'importe quel nom d'utilisateur/mot de passe
    // Dans un vrai système, vous valideriez les informations d'identification auprès du backend
    setIsLoading(true);

    // Simuler une requête d'API
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Si l'utilisateur se connecte avec "super-admin", lui donner ce rôle
    const role = username.toLowerCase() === 'super-admin' ? 'super-admin' : 'admin';

    const mockUser: User = {
      id: '1',
      username,
      role,
      email: `${username}@example.com`,
      fullName: `${username.charAt(0).toUpperCase() + username.slice(1)} User`
    };

    localStorage.setItem('user', JSON.stringify(mockUser));
    setUser(mockUser);

    // Si l'utilisateur se connecte normalement, supprimer toute session par lien magique
    localStorage.removeItem('currentMagicLink');
    setCurrentMagicLink(null);

    setIsLoading(false);
  };

  const logout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('currentMagicLink');
    setUser(null);
    setCurrentMagicLink(null);
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      login,
      logout,
      isAuthenticated: !!user || !!currentMagicLink,
      currentMagicLink,
      isMagicLinkSession: !user && !!currentMagicLink
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
