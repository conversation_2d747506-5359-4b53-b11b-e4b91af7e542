
import LanguageSwitcher from '@/components/LanguageSwitcher';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/contexts/AuthContext';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const Login = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const { login, isLoading } = useAuth();
  const { t } = useTranslation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!username || !password) {
      setError(t('auth.fillAllFields') || 'Veuillez remplir tous les champs');
      return;
    }

    try {
      await login(username, password);
    } catch (error) {
      setError(t('auth.incorrectCredentials') || 'Identifiants incorrects');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="w-full max-w-md">
        <div className="mb-8 text-center relative">
          <div className="absolute right-0 top-0">
            <LanguageSwitcher showLabel={true} />
          </div>
          <h1 className="text-4xl font-bold text-primary mb-2">{t('app.title') || 'Camp des jeunes'}</h1>
          <p className="text-gray-600">{t('app.subtitle') || 'Gestion des camps de jeunes'}</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>{t('auth.login') || 'Connexion'}</CardTitle>
            <CardDescription>
              {t('auth.loginDescription') || 'Entrez vos identifiants pour accéder à la plateforme'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit}>
              {error && (
                <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-600 rounded-md text-sm">
                  {error}
                </div>
              )}

              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="username" className="text-sm font-medium">
                    {t('auth.username') || 'Nom d\'utilisateur'}
                  </label>
                  <Input
                    id="username"
                    type="text"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    placeholder={t('auth.usernamePlaceholder') || 'Votre nom d\'utilisateur'}
                    autoComplete="username"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label htmlFor="password" className="text-sm font-medium">
                      {t('auth.password') || 'Mot de passe'}
                    </label>
                    <a href="#" className="text-xs text-primary hover:underline">
                      {t('auth.forgotPassword') || 'Mot de passe oublié?'}
                    </a>
                  </div>
                  <Input
                    id="password"
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder={t('auth.passwordPlaceholder') || 'Votre mot de passe'}
                    autoComplete="current-password"
                    required
                  />
                </div>
              </div>

              <Button type="submit" className="w-full mt-6" disabled={isLoading}>
                {isLoading ? (
                  <span className="flex items-center">
                    <span className="animate-spin h-4 w-4 mr-2 border-2 border-b-0 border-r-0 border-white rounded-full"></span>
                    {t('common.loading') || 'Connexion en cours...'}
                  </span>
                ) : (
                  t('auth.loginButton') || 'Se connecter'
                )}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex justify-center border-t p-4">
            <p className="text-sm text-gray-500">
              {t('auth.contactAdmin') || 'Pour obtenir un compte, veuillez contacter l\'administrateur'}
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
