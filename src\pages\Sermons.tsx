
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";
import { useLanguage } from "@/contexts/LanguageContext";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { enUS, fr } from "date-fns/locale";
import { AlignmentType, Document, HeadingLevel, Packer, Paragraph } from "docx";
import { saveAs } from "file-saver";
import { Book, ChevronDown, ChevronUp, Download, Edit, MoreHorizontal, PlusCircle, Printer, RefreshCw } from "lucide-react";
import { useRef, useState } from "react";
import { useTranslation } from "react-i18next";

const Sermons = () => {
  const { sermons, addSermon, updateSermon, getSermonById, isLoading, refreshData } = useSupabaseData();
  const { t } = useTranslation();
  const { language } = useLanguage();
  const dateLocale = language === 'fr' ? fr : enUS;
  const [expandedSermon, setExpandedSermon] = useState<string | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentSermonId, setCurrentSermonId] = useState<string | null>(null);
  const sermonContentRef = useRef<HTMLDivElement>(null);

  // Form state
  const [preacher, setPreacher] = useState("");
  const [theme, setTheme] = useState("");
  const [date, setDate] = useState("");
  const [passages, setPassages] = useState("");
  const [content, setContent] = useState("");

  const toggleSermon = (id: string) => {
    setExpandedSermon(expandedSermon === id ? null : id);
  };

  const resetForm = () => {
    setPreacher("");
    setTheme("");
    setDate("");
    setPassages("");
    setContent("");
    setEditMode(false);
    setCurrentSermonId(null);
  };

  const handleOpenDialog = () => {
    resetForm();
    setIsDialogOpen(true);
  };

  const handleEditSermon = (sermonId: string) => {
    const sermon = getSermonById(sermonId);
    if (sermon) {
      setCurrentSermonId(sermonId);
      setPreacher(sermon.preacher);
      setTheme(sermon.theme);
      setDate(format(new Date(sermon.date), "yyyy-MM-dd"));
      setPassages(sermon.passages.join("; "));
      setContent(sermon.content);
      setEditMode(true);
      setIsDialogOpen(true);
    }
  };

  const handlePrintSermon = (sermonId: string) => {
    const sermon = getSermonById(sermonId);
    if (!sermon) return;

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      toast({
        title: t('common.error'),
        description: t('sermons.printError'),
        variant: "destructive",
      });
      return;
    }

    printWindow.document.write(`
      <html>
        <head>
          <title>${sermon.theme} - ${sermon.preacher}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            h1 { text-align: center; margin-bottom: 10px; }
            h2 { text-align: center; margin-top: 0; color: #555; font-size: 1.2em; }
            .meta { text-align: center; margin-bottom: 30px; color: #777; }
            .references { text-align: center; margin-bottom: 30px; font-style: italic; }
            .content { max-width: 800px; margin: 0 auto; }
            p { margin-bottom: 15px; text-align: justify; }
            @media print {
              body { margin: 20px; }
            }
          </style>
        </head>
        <body>
          <h1>${sermon.theme}</h1>
          <h2>${t('sermons.by')} ${sermon.preacher}</h2>
          <div class="meta">
            ${format(new Date(sermon.date), "EEEE dd MMMM yyyy", { locale: dateLocale })}
          </div>
          <div class="references">
            ${t('sermons.references')}: ${sermon.passages.join("; ")}
          </div>
          <div class="content">
            ${sermon.content.split("\n").map(paragraph => `<p>${paragraph}</p>`).join("")}
          </div>
          <div style="text-align: center; margin-top: 50px; font-size: 0.8em; color: #999;">
            ${t('sermons.printedOn')} ${format(new Date(), "dd/MM/yyyy à HH:mm", { locale: dateLocale })}
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Attendre que le contenu soit chargé avant d'imprimer
    printWindow.onload = function () {
      printWindow.focus();
      printWindow.print();
      // Ne pas fermer la fenêtre après l'impression pour permettre à l'utilisateur de voir le contenu
    };
  };

  const handleDownloadWord = async (sermonId: string) => {
    const sermon = getSermonById(sermonId);
    if (!sermon) return;

    try {
      toast({
        title: t('sermons.preparingDownload'),
        description: t('sermons.generatingDocument'),
      });

      // Create a new document
      const doc = new Document({
        sections: [
          {
            properties: {},
            children: [
              new Paragraph({
                text: sermon.theme,
                heading: HeadingLevel.HEADING_1,
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                text: `${t('sermons.by')} ${sermon.preacher}`,
                heading: HeadingLevel.HEADING_2,
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                text: format(new Date(sermon.date), "EEEE dd MMMM yyyy", { locale: dateLocale }),
                alignment: AlignmentType.CENTER,
              }),
              new Paragraph({
                text: `${t('sermons.references')}: ${sermon.passages.join("; ")}`,
                alignment: AlignmentType.CENTER,
                italics: true,
              }),
              new Paragraph({ text: "" }), // Empty paragraph for spacing
              ...sermon.content.split("\n").map(paragraph =>
                new Paragraph({
                  text: paragraph,
                  spacing: {
                    after: 200, // 10 points
                  }
                })
              ),
            ],
          },
        ],
      });

      // Generate and save the document
      const buffer = await Packer.toBlob(doc);
      saveAs(buffer, `sermon_${sermon.preacher.replace(/\s+/g, '_')}_${format(new Date(sermon.date), "yyyy-MM-dd")}.docx`);

      toast({
        title: t('common.success'),
        description: t('sermons.downloadSuccess'),
      });
    } catch (error) {
      console.error("Erreur lors de la génération du document Word:", error);
      toast({
        title: t('common.error'),
        description: t('sermons.downloadError'),
        variant: "destructive",
      });
    }
  };

  const handleSaveSermon = () => {
    if (!preacher || !theme || !date || !passages || !content) {
      toast({
        title: t('common.error'),
        description: t('sermons.fillAllFields'),
        variant: "destructive",
      });
      return;
    }

    try {
      if (editMode && currentSermonId) {
        updateSermon(currentSermonId, {
          preacher,
          theme,
          date: new Date(date),
          passages: passages.split(";").map(passage => passage.trim()),
          content,
        });

        toast({
          title: t('common.success'),
          description: t('sermons.updateSuccess'),
        });
      } else {
        addSermon({
          preacher,
          theme,
          date: new Date(date),
          passages: passages.split(";").map(passage => passage.trim()),
          content,
        });

        toast({
          title: t('common.success'),
          description: t('sermons.addSuccess'),
        });
      }

      setIsDialogOpen(false);
      resetForm();
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('sermons.operationError'),
        variant: "destructive",
      });
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('sermons.title')}</h1>
          <p className="text-gray-500">{t('sermons.subtitle')}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.sermons}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.sermons ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
          <Button onClick={handleOpenDialog}>
            <PlusCircle className="h-4 w-4 mr-2" />
            {t('sermons.newSermon')}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('sermons.sermonList')}</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading.sermons ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('sermons.date')}</TableHead>
                    <TableHead>{t('sermons.theme')}</TableHead>
                    <TableHead>{t('sermons.preacher')}</TableHead>
                    <TableHead>{t('sermons.references')}</TableHead>
                    <TableHead className="w-24">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sermons.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                        {t('sermons.noSermons')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    sermons.map((sermon) => [
                      <TableRow key={`row-${sermon.id}`}>
                        <TableCell>{format(new Date(sermon.date), "dd MMMM yyyy", { locale: dateLocale })}</TableCell>
                        <TableCell>{sermon.theme}</TableCell>
                        <TableCell>{sermon.preacher}</TableCell>
                        <TableCell>
                          {sermon.passages.join("; ")}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => toggleSermon(sermon.id)}
                            >
                              {expandedSermon === sermon.id ? (
                                <ChevronUp className="h-4 w-4 mr-1" />
                              ) : (
                                <ChevronDown className="h-4 w-4 mr-1" />
                              )}
                              {expandedSermon === sermon.id ? t('sermons.collapse') : t('sermons.expand')}
                            </Button>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleEditSermon(sermon.id)}>
                                  <Edit className="h-4 w-4 mr-2" />
                                  {t('sermons.modify')}
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handlePrintSermon(sermon.id)}>
                                  <Printer className="h-4 w-4 mr-2" />
                                  {t('sermons.print')}
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleDownloadWord(sermon.id)}>
                                  <Download className="h-4 w-4 mr-2" />
                                  {t('sermons.download')}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </TableRow>,
                      expandedSermon === sermon.id && (
                        <TableRow key={`expanded-${sermon.id}`}>
                          <TableCell colSpan={5} className="bg-gray-50">
                            <div className="p-4">
                              <div className="flex items-start mb-4">
                                <Book className="h-5 w-5 mr-2 text-primary mt-1" />
                                <div>
                                  <h3 className="font-medium text-lg">{sermon.theme}</h3>
                                  <p className="text-sm text-gray-500 mb-2">
                                    {sermon.preacher} · {format(new Date(sermon.date), "dd MMMM yyyy", { locale: dateLocale })}
                                  </p>
                                  <div className="prose max-w-none" ref={sermonContentRef}>
                                    {sermon.content.split("\n").map((paragraph, idx) => (
                                      <p key={idx} className="mb-2">{paragraph}</p>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    ].filter(Boolean))
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={(open) => {
        if (!open) resetForm();
        setIsDialogOpen(open);
      }}>
        <DialogContent className="max-w-md max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{editMode ? t('sermons.editSermon') : t('sermons.newSermon')}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4 flex-1 overflow-y-auto pr-2">
            <div className="space-y-2">
              <Label htmlFor="preacher">{t('sermons.preacher')}</Label>
              <Input
                id="preacher"
                value={preacher}
                onChange={(e) => setPreacher(e.target.value)}
                placeholder={t('sermons.preacherPlaceholder')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="theme">{t('sermons.theme')}</Label>
              <Input
                id="theme"
                value={theme}
                onChange={(e) => setTheme(e.target.value)}
                placeholder={t('sermons.themePlaceholder')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="date">{t('sermons.date')}</Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="passages">{t('sermons.references')}</Label>
              <Input
                id="passages"
                value={passages}
                onChange={(e) => setPassages(e.target.value)}
                placeholder={t('sermons.referencesPlaceholder')}
              />
              <p className="text-xs text-gray-500">{t('sermons.referencesHelp')}</p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="content">{t('sermons.content')}</Label>
              <Textarea
                id="content"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                placeholder={t('sermons.contentPlaceholder')}
                rows={8}
              />
            </div>
          </div>
          <DialogFooter className="pt-2 border-t mt-2">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleSaveSermon}>
              {editMode ? t('common.save') : t('common.add')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Sermons;
