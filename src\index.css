@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Raleway:wght@400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-raleway font-bold;
  }
}

@layer components {
  .page-container {
    @apply container px-4 py-8 mx-auto;
  }

  .section-title {
    @apply text-2xl md:text-3xl font-bold text-primary mb-6 font-raleway;
  }

  .card-container {
    @apply bg-white rounded-lg shadow-md p-6 border border-gray-100;
  }

  .stats-card {
    @apply bg-white rounded-lg shadow-sm p-4 border border-gray-100 hover:shadow-md transition-all;
  }

  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .btn-primary {
    @apply bg-primary hover:bg-primary-hover text-white font-medium py-2 px-4 rounded-md transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary-hover text-secondary-foreground font-medium py-2 px-4 rounded-md transition-colors;
  }

  .btn-outline {
    @apply border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-md transition-colors;
  }

  .link-text {
    @apply text-primary hover:text-primary-hover underline transition-colors;
  }

  .table-container {
    @apply w-full overflow-x-auto;
  }

  .data-table {
    @apply min-w-full bg-white;
  }

  .data-table th {
    @apply px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b;
  }

  .data-table td {
    @apply px-4 py-3 border-b border-gray-100;
  }

  .data-table tr:hover {
    @apply bg-gray-50;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-blue {
    @apply bg-blue-100 text-blue-800;
  }

  .badge-green {
    @apply bg-green-100 text-green-800;
  }

  /* Styles personnalisés pour les barres de défilement */
  .max-h-32 {
    scrollbar-width: thin;
    scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
  }

  .max-h-32::-webkit-scrollbar {
    width: 6px;
  }

  .max-h-32::-webkit-scrollbar-track {
    background: transparent;
  }

  .max-h-32::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
  }

  .max-h-32::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.8);
  }

  .badge-yellow {
    @apply bg-yellow-100 text-yellow-800;
  }

  .badge-red {
    @apply bg-red-100 text-red-800;
  }
}