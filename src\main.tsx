import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './i18n'; // Import i18n configuration
import './index.css';

// Suppress specific React warnings
const originalConsoleError = console.error;
console.error = function filterWarnings(msg, ...args) {
    // Filter out specific warnings
    if (typeof msg === 'string' && (
        msg.includes('Support for defaultProps will be removed from memo components') ||
        msg.includes('Invalid prop `data-lov-id` supplied to `React.Fragment`')
    )) {
        return;
    }
    originalConsoleError(msg, ...args);
};

createRoot(document.getElementById("root")!).render(<App />);
