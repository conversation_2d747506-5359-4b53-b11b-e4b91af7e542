
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { Building, ChevronLeft, ChevronRight, PlusCircle, RefreshCw, Users } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";

const Assemblies = () => {
  const {
    assemblies,
    districts,
    regions,
    addAssembly,
    getDistrictById,
    getRegionById,
    getDistrictsByRegion,
    getYouthsByAssembly,
    isLoading,
    refreshData
  } = useSupabaseData();

  const { t } = useTranslation();
  const [newAssemblyName, setNewAssemblyName] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedDistrict, setSelectedDistrict] = useState("");
  const [filteredDistricts, setFilteredDistricts] = useState(districts);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // State for assembly details dialog
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedAssemblyId, setSelectedAssemblyId] = useState<string | null>(null);

  // Pagination state for youth list in details dialog
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20; // Number of youths to display per page

  // Update districts when region changes
  const handleRegionChange = (value: string) => {
    setSelectedRegion(value);
    setSelectedDistrict("");

    if (value) {
      const districtsInRegion = getDistrictsByRegion(value);
      setFilteredDistricts(districtsInRegion);
    } else {
      setFilteredDistricts([]);
    }

    if (errors.region) {
      setErrors(prev => ({ ...prev, region: "" }));
    }
  };

  const handleAddAssembly = (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: Record<string, string> = {};

    if (!newAssemblyName.trim()) {
      newErrors.name = t('assemblies.assemblyNameRequired');
    }

    if (!selectedRegion) {
      newErrors.region = t('regions.regionRequired');
    }

    if (!selectedDistrict) {
      newErrors.district = t('assemblies.districtRequired');
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Check if assembly already exists in the selected district
    if (assemblies.some(
      assembly =>
        assembly.name.toLowerCase() === newAssemblyName.trim().toLowerCase() &&
        assembly.districtId === selectedDistrict
    )) {
      setErrors({ name: t('assemblies.assemblyExists') });
      return;
    }

    try {
      addAssembly(newAssemblyName.trim(), selectedDistrict);
      toast({
        title: t('assemblies.assemblyAdded'),
        description: t('assemblies.assemblyAddedSuccess'),
      });
      setNewAssemblyName("");
      setErrors({});
    } catch (error) {
      console.error("Error adding assembly:", error);
      toast({
        title: t('common.error'),
        description: t('assemblies.assemblyAddError'),
        variant: "destructive",
      });
    }
  };

  const handleShowDetails = (assemblyId: string) => {
    setSelectedAssemblyId(assemblyId);
    setIsDetailsDialogOpen(true);
    setCurrentPage(1); // Reset to first page when opening details
  };

  // Reset pagination when dialog closes
  useEffect(() => {
    if (!isDetailsDialogOpen) {
      setCurrentPage(1);
    }
  }, [isDetailsDialogOpen]);

  // Get the selected assembly object
  const selectedAssembly = selectedAssemblyId
    ? assemblies.find(assembly => assembly.id === selectedAssemblyId)
    : null;

  // Get district and region for the selected assembly
  const selectedAssemblyDistrict = selectedAssembly
    ? getDistrictById(selectedAssembly.districtId)
    : null;

  const selectedAssemblyRegion = selectedAssemblyDistrict
    ? getRegionById(selectedAssemblyDistrict.regionId)
    : null;

  // Get youths for the selected assembly
  const assemblyYouths = selectedAssemblyId
    ? getYouthsByAssembly(selectedAssemblyId)
    : [];

  // Calculate pagination for youth list
  const totalPages = Math.ceil(assemblyYouths.length / itemsPerPage);
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentYouths = assemblyYouths.slice(indexOfFirstItem, indexOfLastItem);

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('assemblies.title')}</h1>
        <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.assemblies}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.assemblies ? 'animate-spin' : ''}`} />
          {t('common.refresh')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('assemblies.addAssembly')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddAssembly} className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder={t('assemblies.assemblyNamePlaceholder')}
                  value={newAssemblyName}
                  onChange={(e) => {
                    setNewAssemblyName(e.target.value);
                    if (errors.name) {
                      setErrors(prev => ({ ...prev, name: "" }));
                    }
                  }}
                  className={errors.name ? "border-destructive" : ""}
                />
                {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
              </div>

              <div className="space-y-2">
                <Select
                  value={selectedRegion}
                  onValueChange={handleRegionChange}
                >
                  <SelectTrigger className={errors.region ? "border-destructive" : ""}>
                    <SelectValue placeholder={t('districts.selectRegion')} />
                  </SelectTrigger>
                  <SelectContent>
                    {regions.map((region) => (
                      <SelectItem key={region.id} value={region.id}>
                        {region.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.region && <p className="text-sm text-destructive">{errors.region}</p>}
              </div>

              <div className="space-y-2">
                <Select
                  value={selectedDistrict}
                  onValueChange={(value) => {
                    setSelectedDistrict(value);
                    if (errors.district) {
                      setErrors(prev => ({ ...prev, district: "" }));
                    }
                  }}
                  disabled={!selectedRegion}
                >
                  <SelectTrigger className={errors.district ? "border-destructive" : ""}>
                    <SelectValue placeholder={t('assemblies.selectDistrict')} />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredDistricts.map((district) => (
                      <SelectItem key={district.id} value={district.id}>
                        {district.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.district && <p className="text-sm text-destructive">{errors.district}</p>}
              </div>

              <Button type="submit" className="w-full">
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('common.add')}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('assemblies.assemblyList')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.name')}</TableHead>
                    <TableHead>{t('assemblies.district')}</TableHead>
                    <TableHead>{t('districts.region')}</TableHead>
                    <TableHead className="w-24">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assemblies.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={4} className="text-center py-4 text-gray-500">
                        {t('assemblies.noAssemblies')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    assemblies.map((assembly) => {
                      const district = getDistrictById(assembly.districtId);
                      const region = district ? getRegionById(district.regionId) : null;

                      return (
                        <TableRow key={assembly.id}>
                          <TableCell>{assembly.name}</TableCell>
                          <TableCell>{district?.name}</TableCell>
                          <TableCell>{region?.name}</TableCell>
                          <TableCell>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleShowDetails(assembly.id)}
                            >
                              {t('assemblies.details')}
                            </Button>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Assembly Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('assemblies.assemblyDetails')}</DialogTitle>
          </DialogHeader>

          {selectedAssembly && (
            <div className="space-y-6 py-4 overflow-y-auto">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">{selectedAssembly.name}</h3>
                </div>
                <div className="space-y-1 text-sm text-gray-500">
                  <p>
                    {t('assemblies.district')}: {selectedAssemblyDistrict?.name}
                  </p>
                  <p>
                    {t('districts.region')}: {selectedAssemblyRegion?.name}
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  <h3 className="text-md font-medium">{t('youths.title')}</h3>
                </div>
                <p className="text-sm">
                  {t('common.total')}: <span className="font-medium">{assemblyYouths.length}</span>
                </p>
                <div className="flex gap-4 text-sm">
                  <p>
                    {t('common.male')}: <span className="font-medium">
                      {assemblyYouths.filter(youth => youth.gender === 'male').length}
                    </span>
                  </p>
                  <p>
                    {t('common.female')}: <span className="font-medium">
                      {assemblyYouths.filter(youth => youth.gender === 'female').length}
                    </span>
                  </p>
                </div>

                {assemblyYouths.length > 0 && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium mb-2">{t('youths.youthList')}:</h4>
                    <ScrollArea className="h-[200px] pr-4">
                      <ul className="space-y-1 pl-7 list-disc text-sm">
                        {currentYouths.map(youth => (
                          <li key={youth.id}>{youth.firstName} {youth.lastName}</li>
                        ))}
                      </ul>
                    </ScrollArea>

                    {/* Pagination controls */}
                    {totalPages > 1 && (
                      <div className="flex justify-between items-center mt-4 text-sm">
                        <div>
                          {t('common.showing')} {indexOfFirstItem + 1}-{Math.min(indexOfLastItem, assemblyYouths.length)} {t('common.of')} {assemblyYouths.length}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                            disabled={currentPage === 1}
                          >
                            <ChevronLeft className="h-4 w-4" />
                          </Button>
                          <span>{currentPage} / {totalPages}</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                            disabled={currentPage === totalPages}
                          >
                            <ChevronRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Assemblies;
