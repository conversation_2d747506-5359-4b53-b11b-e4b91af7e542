{"app": {"title": "Camp des jeunes", "subtitle": "Gestion des jeunes"}, "sidebar": {"collapse": "R<PERSON><PERSON><PERSON> le menu", "expand": "<PERSON><PERSON><PERSON><PERSON> le menu", "dashboard": "Tableau de bord", "participants": "Participants", "youthList": "Liste des jeunes", "registrationLinks": "Liens d'inscription", "organization": "Organisation", "regions": "Régions", "districts": "Districts", "assemblies": "Assemblées", "permissions": "Permissions", "sermons": "<PERSON><PERSON>", "programs": "Programmes", "settings": "Paramètres", "accessRights": "Droits d'accès", "logout": "Déconnexion"}, "common": {"search": "Rechercher...", "searchByName": "Rechercher par nom ou prénom...", "add": "Ajouter", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Annuler", "save": "Enregistrer", "create": "<PERSON><PERSON><PERSON>", "actions": "Actions", "details": "Détails", "view": "Voir", "name": "Nom", "firstName": "Prénom", "lastName": "Nom", "age": "Âge", "gender": "Genre", "male": "<PERSON><PERSON><PERSON><PERSON>", "female": "Fé<PERSON>n", "total": "Total", "email": "Email", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "date": "Date", "startDate": "Date de début", "endDate": "Date de fin", "status": "Statut", "active": "Actif", "inactive": "Inactif", "approved": "Approu<PERSON><PERSON>", "pending": "En attente", "rejected": "<PERSON><PERSON><PERSON>", "description": "Description", "notes": "Notes", "submit": "So<PERSON><PERSON><PERSON>", "confirm": "Confirmer", "success": "Su<PERSON>ès", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "info": "Information", "loading": "Chargement...", "noData": "<PERSON><PERSON><PERSON> donnée disponible", "all": "Tous", "none": "Aucun", "yes": "O<PERSON>", "no": "Non", "back": "Retour", "next": "Suivant", "previous": "Précédent", "copy": "<PERSON><PERSON><PERSON>", "copied": "<PERSON><PERSON><PERSON>", "share": "Partager", "print": "<PERSON><PERSON><PERSON><PERSON>", "printDate": "Date d'impression", "printSuccess": "Impression réussie", "download": "Télécharger", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "export": "Exporter", "language": "<PERSON><PERSON>", "french": "Français", "english": "<PERSON><PERSON><PERSON>", "browserLanguage": "<PERSON><PERSON>", "languageDetected": "Langue détectée", "usingBrowserLanguage": "Utilisation de la langue de votre navigateur : {{language}}", "region": "Région", "district": "District", "assembly": "Assemblée", "location": "Emplacement", "unknown": "Inconnu", "optional": "Optionnel", "refresh": "Actualiser", "dataRefreshed": "Données actualisées", "errorRefreshingData": "Erreur lors de l'actualisation des données", "noSearchResults": "Aucun résultat de recherche", "registrationDate": "Date d'inscription", "id": "ID", "showing": "Affichage", "of": "sur", "clearFilters": "Effacer les filtres", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "link": "<PERSON><PERSON>"}, "auth": {"login": "Connexion", "logout": "Déconnexion", "username": "Nom d'utilisateur", "password": "Mot de passe", "rememberMe": "Se souvenir de moi", "forgotPassword": "Mot de passe oublié ?", "loginButton": "Se connecter", "accessDenied": "<PERSON><PERSON>ès refusé", "accessDeniedMessage": "Vous n'avez pas les droits nécessaires pour accéder à cette page", "loginDescription": "Entrez vos identifiants pour accéder à la plateforme", "usernamePlaceholder": "Votre nom d'utilisateur", "passwordPlaceholder": "Votre mot de passe", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "incorrectCredentials": "Identifiants incorrects", "contactAdmin": "Pour obtenir un compte, veuillez contacter l'administrateur"}, "dashboard": {"title": "Tableau de bord", "welcome": "Bienvenue sur la gestion du camp de jeunes", "resetData": "Réinitialiser les données", "confirmClearData": "Êtes-vous sûr de vouloir effacer toutes les données ? Cette action est irréversible.", "dataCleared": "<PERSON><PERSON><PERSON> effacées", "dataClearedSuccess": "Toutes les données ont été effacées avec succès.", "totalYouths": "Total des jeunes", "totalRegions": "Total des régions", "totalDistricts": "Total des districts", "totalAssemblies": "Total des assemblées", "recentRegistrations": "Inscriptions récentes", "upcomingPrograms": "Programmes à venir", "pendingPermissions": "Permissions en attente", "sermonsRecorded": "Ser<PERSON> enregistrés", "youthDistribution": "Répartition des jeunes", "addYouthsToSeeStats": "Ajoutez des jeunes pour voir les statistiques", "todayProgram": "Programme du jour", "responsible": "Responsable", "noProgramToday": "Aucun programme pour aujourd'hui", "addActivitiesInProgramSection": "Ajoutez des activités dans la section Programme", "statisticsByRegion": "Statistiques par région", "districts": "district(s)", "filteringByRegion": "Filtrage par région", "filteringByDistrict": "Filtrage par district"}, "youths": {"title": "Liste des jeunes", "addYouth": "A<PERSON>ter un jeune", "editYouth": "Modifier un jeune", "deleteYouth": "Supprimer un jeune", "youthDetails": "<PERSON>é<PERSON> du jeune", "personalInfo": "Informations personnelles", "contactInfo": "Informations de contact", "churchInfo": "Informations d'église", "paymentHistory": "Historique des paiements", "permissionHistory": "Historique des permissions", "noYouths": "Aucun jeune trouvé", "manageParticipants": "<PERSON><PERSON><PERSON> la liste des participants au camp", "allRegions": "Toutes les régions", "allDistricts": "Tous les districts", "allAssemblies": "Toutes les assemblées", "registrationDate": "Date d'inscription", "totalCount": "Nombre total", "printedOn": "Imprimé le", "youthSingular": "jeune", "youthsPlural": "jeunes", "firstNameRequired": "Le prénom est requis", "lastNameRequired": "Le nom est requis", "assemblyRequired": "L'assemblée est requise", "addedSuccessfully": "Le jeune a été ajouté avec succès", "errorAdding": "Une erreur est survenue lors de l'ajout du jeune", "selectRegion": "Sélectionner une région", "selectDistrict": "Sélectionner un district", "selectAssembly": "Sélectionner une assemblée", "searchPlaceholder": "Rechercher par nom...", "subtitle": "Voir et gérer les participants jeunes", "filters": "Filtres", "noYouthsFound": "Aucun jeune trouvé", "tryDifferentFilters": "Essayez différents filtres ou effacez la recherche", "youths": "jeunes", "youthList": "Liste des jeunes", "errorLoadingDetails": "Erreur lors du chargement des détails du jeune", "detailsUpdated": "<PERSON>é<PERSON> du jeune mis à jour avec succès", "updateError": "Erreur lors de la mise à jour des détails du jeune", "fillAllRequiredFields": "Veuillez remplir tous les champs obligatoires", "notFound": "Jeune non trouvé", "notFoundDescription": "Le jeune demandé n'a pas pu être trouvé", "details": "<PERSON>é<PERSON> du jeune", "detailsDescription": "Voir et gérer les informations du jeune", "registeredOn": "Inscrit le", "editDetails": "Modifier les détails du jeune", "firstName": "Prénom", "lastName": "Nom", "gender": "Genre", "selectGender": "Sélectionner le genre", "printAll": "Imprimer tous les jeunes", "printFiltered": "Imprimer la liste filtrée"}, "accessRights": {"title": "Gestion des droits d'accès", "subtitle": "<PERSON><PERSON><PERSON> les droits d'accès et les liens magiques", "accessRights": "Droits d'accès", "accessRightsList": "Liste des droits d'accès configurés", "magicLinks": "Liens magiques", "magicLinksList": "Liste des liens d'accès générés", "accessLogs": "Journaux d'accès", "accessLogsDescription": "Historique des accès via les liens magiques", "newAccessRight": "Nouveau droit d'accès", "editAccessRight": "Modifier le droit d'accès", "deleteAccessRight": "Supp<PERSON><PERSON> le droit d'accès", "features": "Fonctionnalités", "accessType": "Type d'accès", "access": "Accès", "none": "Aucun", "read": "Lecture", "write": "Écriture", "readWrite": "Lecture et écriture", "createMagicLink": "<PERSON><PERSON>er un lien magique", "newMagicLink": "Nouveau lien magique", "createLinkDescription": "Créez un nouveau lien magique pour partager l'accès à des fonctionnalités spécifiques", "magicLinkName": "Nom du lien", "magicLinkNamePlaceholder": "Entrez un nom pour ce lien magique", "expirationDate": "Date d'expiration", "creationDate": "Date de création", "activeLinks": "Liens actifs", "accessRight": "<PERSON><PERSON> d'a<PERSON>", "selectAccessRight": "Sélectionner un droit d'accès", "noExpiration": "Pas d'expiration", "link": "<PERSON><PERSON>", "revoke": "Révoquer", "revoked": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "views": "<PERSON><PERSON>", "edits": "Modifications", "accessDate": "Date d'accès", "action": "Action", "feature": "Fonctionnalité", "details": "Détails", "fillAllFields": "Veuillez remplir tous les champs obligatoires", "loginRequired": "Vous devez être connecté pour créer un droit d'accès", "rightCreated": "Le droit d'accès a été créé avec succès", "rightCreateError": "Une erreur est survenue lors de la création du droit d'accès", "rightDeleted": "Le droit d'accès a été supprimé avec succès", "rightDeleteError": "Une erreur est survenue lors de la suppression du droit d'accès", "linkCreated": "Le lien magique a été créé avec succès", "linkCreateError": "Une erreur est survenue lors de la création du lien magique", "linkRevoked": "Le lien magique a été révoqué avec succès", "linkRevokeError": "Une erreur est survenue lors de la révocation du lien magique", "linkDeleted": "Le lien magique a été supprimé avec succès", "linkDeleteError": "Une erreur est survenue lors de la suppression du lien magique", "linkCopied": "Le lien a été copié dans le presse-papiers", "linkCopyError": "Impossible de copier le lien", "noAccessRights": "Aucun droit d'accès configuré", "noMagicLinks": "Aucun lien magique gén<PERSON>", "featureLabels": {"youths": "Participants", "payments": "Paiements", "permissions": "Permissions", "programs": "Programmes", "sermons": "<PERSON><PERSON>", "regions": "Régions", "districts": "Districts", "assemblies": "Assemblées"}}, "sermons": {"title": "<PERSON><PERSON>", "subtitle": "Archivage des prédications du camp", "newSermon": "Nouveau sermon", "editSermon": "Modifier le sermon", "sermonList": "Liste des sermons", "preacher": "Pré<PERSON>ur", "theme": "Thème", "date": "Date", "references": "Références bibliques", "content": "Contenu", "actions": "Actions", "noSermons": "Aucun sermon enregistré", "collapse": "<PERSON><PERSON><PERSON><PERSON>", "expand": "Voir", "modify": "Modifier", "print": "<PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger (.docx)", "preacherPlaceholder": "Nom du prédicateur", "themePlaceholder": "Thème du <PERSON>", "referencesPlaceholder": "<PERSON> 3:16; <PERSON><PERSON> 8:28", "referencesHelp": "<PERSON><PERSON><PERSON>ez les références par des points-virgules (;)", "contentPlaceholder": "Contenu du sermon...", "by": "Par", "printedOn": "Imprimé le", "preparingDownload": "Préparation du téléchargement", "generatingDocument": "Génération du document Word en cours...", "downloadSuccess": "Le sermon a été téléchargé avec succès", "downloadError": "Une erreur est survenue lors de la génération du document", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs", "updateSuccess": "Le sermon a été mis à jour avec succès", "addSuccess": "Le sermon a été ajouté avec succès", "operationError": "Une erreur est survenue lors de l'opération", "printError": "Impossible d'ouvrir la fenêtre d'impression"}, "programs": {"title": "Titre", "subtitle": "Planification des activités du camp", "newProgram": "Nouveau programme journalier", "editProgram": "Modifier le programme", "programFor": "Programme du", "noPrograms": "Aucun programme défini", "noProgramsSubtitle": "Créez votre premier programme journalier pour le camp", "createProgram": "<PERSON><PERSON><PERSON> le programme", "activities": "Activités", "activity": "Activité", "addActivity": "Ajouter une activité", "removeActivity": "Supprimer l'activité", "atLeastOneActivity": "V<PERSON> devez avoir au moins une activité", "description": "Description", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "responsible": "Responsable", "modify": "Modifier", "print": "<PERSON><PERSON><PERSON><PERSON>", "download": "Télécharger (.docx)", "share": "Partager", "copyLink": "Copier le lien", "linkCopied": "Lien copié dans le presse-papiers", "shareError": "Une erreur est survenue lors de la création du lien de partage", "activityTitleRequired": "Le titre de l'activité est requis", "startTimeRequired": "L'heure de début est requise", "endTimeRequired": "L'heure de fin est requise", "dateRequired": "La date est requise", "programAddSuccess": "Le programme a été ajouté avec succès", "programUpdateSuccess": "Le programme a été mis à jour avec succès", "operationError": "Une erreur est survenue lors de l'opération", "dragToReorder": "Glisser pour réorganiser les activités", "todayProgram": "Programme du jour", "activitiesReordered": "Activités réorganisées", "activitiesReorderedSuccess": "L'ordre des activités a été mis à jour", "testLink": "Tester le lien", "noShareLink": "Aucun lien de partage trouvé. Veuillez d'abord créer un lien de partage.", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs obligatoires pour chaque activité", "programDate": "Date du programme", "activityNumber": "Activité", "activityTitle": "Titre*", "activityTitlePlaceholder": "Titre de l'activité", "responsiblePlaceholder": "Nom du responsable", "descriptionPlaceholder": "Description de l'activité", "saveChanges": "Enregistrer les modifications", "activeLinks": "Liens actifs", "registrationLinksList": "Liste de tous les liens d'inscription générés", "noRegistrationLinks": "Aucun lien d'inscription", "clickCreateLinkHint": "Cliquez sur « Créer un lien » pour générer un nouveau lien d'inscription", "createLink": "<PERSON><PERSON>er un lien", "newLinkCreated": "Nouveau lien créé", "shareLinkWithYouths": "Partagez ce lien avec les jeunes qui souhaitent s'inscrire", "manageRegistrationLinks": "<PERSON><PERSON><PERSON> les liens d'inscription pour les jeunes", "registrations": "Inscriptions", "linkCreated": "Le lien d'inscription a été créé avec succès", "linkDeleted": "Le lien d'inscription a été supprimé avec succès", "testLinkDeleteError": "Les liens de test ne peuvent pas être supprimés", "confirmDeleteLink": "Êtes-vous sûr de vouloir supprimer ce lien d'inscription ?"}, "permissions": {"title": "Demandes de permissions", "subtitle": "<PERSON><PERSON><PERSON> les demandes de permissions des jeunes", "newPermission": "Nouvelle permission", "permissionRequests": "Demandes de permissions", "reason": "<PERSON>son", "period": "Période", "status": "Statut", "approved": "A<PERSON><PERSON><PERSON><PERSON>", "pending": "En attente", "approve": "Approuver", "noPermissions": "Aucune permission demandée", "selectYouth": "Sélectionner un jeune", "reasonPlaceholder": "<PERSON><PERSON> de <PERSON>", "permissionApproved": "Permission approuvée", "permissionApprovedSuccess": "La permission a été approuvée avec succès", "permissionAddSuccess": "La permission a été ajoutée avec succès", "operationError": "Une erreur est survenue lors de l'opération", "fillAllFields": "<PERSON><PERSON><PERSON>z remplir tous les champs"}, "settings": {"title": "Paramètres", "profile": "Profil", "notifications": "Notifications", "security": "Sécurité", "notificationPreferences": "Préférences de notification", "emailNotifications": "Notifications par e-mail", "emailNotificationsDescription": "Recevez des e-mails pour les informations importantes", "pushNotifications": "Notifications push", "pushNotificationsDescription": "Recevez des notifications push pour les informations importantes", "notificationsUpdated": "Paramètres de notification mis à jour", "notificationsUpdatedDescription": "Vos préférences de notification ont été mises à jour.", "securitySettings": "Paramètres de sécurité", "changePassword": "Changer de mot de passe", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmPassword": "Confirmer le mot de passe", "passwordChanged": "Mot de passe changé", "passwordChangedDescription": "Votre mot de passe a été changé avec succès.", "passwordError": "Erreur de mot de passe", "passwordErrorDescription": "Une erreur est survenue lors du changement de votre mot de passe.", "passwordMismatch": "Les mots de passe ne correspondent pas", "passwordRequired": "Le mot de passe est requis", "profileSettings": "Paramètres du profil", "name": "Nom", "email": "Email", "username": "Nom d'utilisateur", "fullName": "Nom complet", "updateProfile": "Mettre à jour le profil", "profileUpdated": "Profil mis à jour", "profileUpdatedDescription": "Votre profil a été mis à jour avec succès.", "profileError": "<PERSON><PERSON><PERSON> de profil", "profileErrorDescription": "Une erreur est survenue lors de la mise à jour de votre profil."}, "regions": {"title": "Gestion des Régions", "addRegion": "Ajouter une région", "regionList": "Liste des régions", "regionName": "Nom de la région", "regionNamePlaceholder": "Nom de la région", "regionNameRequired": "Le nom de la région est requis", "regionExists": "Cette région existe déjà", "regionAdded": "Région ajoutée", "regionAddedSuccess": "La région a été ajoutée avec succès", "regionAddError": "Une erreur est survenue lors de l'ajout de la région", "noRegions": "Aucune région disponible", "details": "Détails", "regionDetails": "Détails de la région", "actions": "Actions"}, "districts": {"title": "Gestion des Districts", "addDistrict": "Ajouter un district", "districtList": "Liste des districts", "districtName": "Nom du district", "districtNamePlaceholder": "Nom du district", "selectRegion": "Sélectionner une région", "districtNameRequired": "Le nom du district est requis", "regionRequired": "La région est requise", "districtExists": "Ce district existe déjà dans la région sélectionnée", "districtAdded": "District ajouté", "districtAddedSuccess": "Le district a été ajouté avec succès", "districtAddError": "Une erreur est survenue lors de l'ajout du district", "noDistricts": "Aucun district disponible", "details": "Détails", "districtDetails": "Détails du district", "actions": "Actions", "region": "Région"}, "assemblies": {"title": "Gestion des Assemblées", "addAssembly": "Ajouter une assemblée", "assemblyList": "Liste des assemblées", "assemblyName": "Nom de l'assemblée", "assemblyNamePlaceholder": "Nom de l'assemblée", "selectDistrict": "Sélectionner un district", "assemblyNameRequired": "Le nom de l'assemblée est requis", "districtRequired": "Le district est requis", "assemblyExists": "Cette assemblée existe déjà dans le district sélectionné", "assemblyAdded": "Assemblée a<PERSON>", "assemblyAddedSuccess": "L'assemblée a été ajoutée avec succès", "assemblyAddError": "Une erreur est survenue lors de l'ajout de l'assemblée", "noAssemblies": "Aucune assemblée disponible", "details": "Détails", "assemblyDetails": "Détails de l'assemblée", "actions": "Actions", "district": "District"}, "notFound": {"title": "404", "message": "Oups ! Page non trouvée", "returnHome": "Retour à l'accueil"}, "payments": {"title": "Paiements", "description": "Historique des paiements pour ce jeune", "add": "Ajouter un paiement", "addPayment": "Ajouter un paiement", "amount": "<PERSON><PERSON>", "method": "Méthode de paiement", "date": "Date de paiement", "reference": "Référence", "referencePlaceholder": "Référence de transaction ou note", "methodCash": "Espèces", "methodMobileMoney": "Mobile Money", "methodBankTransfer": "Virement bancaire", "methodOther": "<PERSON><PERSON>", "selectMethod": "Sélectionner la méthode de paiement", "noPayments": "Aucun paiement enregistré", "totalPaid": "Total payé", "numberOfPayments": "Nombre de paiements", "lastPayment": "Dernier paiement", "summary": "Résumé des paiements", "paymentAdded": "<PERSON><PERSON><PERSON> a<PERSON>", "paymentAddedSuccess": "Le paiement a été ajouté avec succès", "paymentAddError": "Une erreur est survenue lors de l'ajout du paiement", "fillAllRequiredFields": "Veuillez remplir tous les champs obligatoires", "invalidAmount": "Veuillez entrer un montant valide supérieur à zéro", "addError": "<PERSON><PERSON>ur lors de l'ajout du paiement"}, "registration": {"form": "Formulaire d'inscription", "title": "Inscription au camp", "description": "Veu<PERSON>z remplir ce formulaire pour vous inscrire au camp de jeunes.", "submit": "S'inscrire", "submitting": "Inscription en cours...", "contactInfo": "Pour toute question, ve<PERSON><PERSON><PERSON> contacter l'organisateur du camp.", "checkingLink": "Vérification du lien d'inscription...", "invalidLink": {"title": "<PERSON>n invalide", "description": "Ce lien d'inscription n'est pas valide ou a expiré."}, "expiredLink": {"title": "<PERSON>n expiré", "description": "Ce lien d'inscription a expiré."}, "maxReached": {"title": "Nombre maximum atteint", "description": "Le nombre maximum d'inscriptions pour ce lien a été atteint."}, "formError": {"title": "Erreur de formulaire", "description": "Veu<PERSON>z remplir tous les champs obligatoires."}, "error": {"title": "Lien d'inscription invalide", "message": "Le lien d'inscription que vous avez utilisé est invalide, a expiré ou a atteint sa limite d'utilisations.", "description": "Une erreur s'est produite lors de l'inscription. Veuillez réessayer.", "whatToDo": "Que faire ?", "checkLink": "Vérifiez que vous avez utilisé le lien correct", "requestNewLink": "Demandez un nouveau lien d'inscription à l'organisateur", "contactOrganizer": "Contactez l'organisateur pour vous inscrire directement", "closePage": "Fermer cette page"}, "success": {"title": "Inscription réussie !", "message": "Votre inscription au camp de jeunes a été enregistrée avec succès. Nous avons hâte de vous accueillir !", "whatNext": "Que faire maintenant ?", "waitConfirmation": "Attendez la confirmation de votre inscription par l'organisateur", "prepareCamp": "P<PERSON><PERSON>ez-vous pour le camp (dates, affaires à apporter, etc.)", "contactOrganizer": "N'hésitez pas à contacter l'organisateur pour toute question", "closePage": "Fermer cette page"}}}