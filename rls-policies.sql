-- Enable Row Level Security on all tables
ALTER TABLE regions ENABLE ROW LEVEL SECURITY;
ALTER TABLE districts ENABLE ROW LEVEL SECURITY;
ALTER TABLE assemblies ENABLE ROW LEVEL SECURITY;
ALTER TABLE youths ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE permissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE sermons ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE program_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE registration_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE program_share_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE access_rights ENABLE ROW LEVEL SECURITY;
ALTER TABLE magic_links ENABLE ROW LEVEL SECURITY;
ALTER TABLE access_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- Create a function to check if a user has access to a feature
CREATE OR REPLACE FUNCTION auth.has_feature_access(feature_name TEXT, access_type TEXT)
RETURNS BOOLEAN AS $$
DECLARE
  user_id TEXT;
  magic_link_id TEXT;
  access_right_id TEXT;
  has_access BOOLEAN;
BEGIN
  -- Get the current user ID
  user_id := auth.uid();
  
  -- If no user is authenticated, check if there's a magic link session
  IF user_id IS NULL THEN
    -- This would need to be implemented based on how you track magic link sessions
    -- For example, you might store the current magic link ID in a session variable
    -- or pass it as a header in API requests
    RETURN FALSE;
  END IF;
  
  -- Check if the user is a super-admin (they have access to everything)
  PERFORM 1 FROM users WHERE id = user_id AND role = 'super-admin';
  IF FOUND THEN
    RETURN TRUE;
  END IF;
  
  -- Check if the user has access through a magic link
  -- This is a simplified example and would need to be adapted to your actual schema
  SELECT ar.id INTO access_right_id
  FROM magic_links ml
  JOIN access_rights ar ON ml.access_right_id = ar.id
  WHERE ml.user_id = user_id
    AND ml.active = TRUE
    AND (ml.expires_at IS NULL OR ml.expires_at > NOW());
  
  IF access_right_id IS NOT NULL THEN
    -- Check if the access right includes the requested feature and access type
    SELECT EXISTS (
      SELECT 1
      FROM access_rights ar, jsonb_array_elements(ar.features) AS feature
      WHERE ar.id = access_right_id
        AND feature->>'feature' = feature_name
        AND (
          (access_type = 'read' AND (feature->>'access' = 'read' OR feature->>'access' = 'read-write'))
          OR
          (access_type = 'write' AND feature->>'access' = 'read-write')
        )
    ) INTO has_access;
    
    RETURN has_access;
  END IF;
  
  -- If we get here, the user doesn't have access
  RETURN FALSE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create policies for regions table
CREATE POLICY "Allow read access to regions for all authenticated users"
  ON regions FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('regions', 'read'));

CREATE POLICY "Allow insert/update/delete access to regions for authorized users"
  ON regions FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('regions', 'write'));

-- Create policies for districts table
CREATE POLICY "Allow read access to districts for all authenticated users"
  ON districts FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('districts', 'read'));

CREATE POLICY "Allow insert/update/delete access to districts for authorized users"
  ON districts FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('districts', 'write'));

-- Create policies for assemblies table
CREATE POLICY "Allow read access to assemblies for all authenticated users"
  ON assemblies FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('assemblies', 'read'));

CREATE POLICY "Allow insert/update/delete access to assemblies for authorized users"
  ON assemblies FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('assemblies', 'write'));

-- Create policies for youths table
CREATE POLICY "Allow read access to youths for all authenticated users"
  ON youths FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('youths', 'read'));

CREATE POLICY "Allow insert/update/delete access to youths for authorized users"
  ON youths FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('youths', 'write'));

-- Create policies for payments table
CREATE POLICY "Allow read access to payments for all authenticated users"
  ON payments FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('payments', 'read'));

CREATE POLICY "Allow insert/update/delete access to payments for authorized users"
  ON payments FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('payments', 'write'));

-- Create policies for permissions table
CREATE POLICY "Allow read access to permissions for all authenticated users"
  ON permissions FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('permissions', 'read'));

CREATE POLICY "Allow insert/update/delete access to permissions for authorized users"
  ON permissions FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('permissions', 'write'));

-- Create policies for sermons table
CREATE POLICY "Allow read access to sermons for all authenticated users"
  ON sermons FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('sermons', 'read'));

CREATE POLICY "Allow insert/update/delete access to sermons for authorized users"
  ON sermons FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('sermons', 'write'));

-- Create policies for daily_programs table
CREATE POLICY "Allow read access to daily_programs for all authenticated users"
  ON daily_programs FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('programs', 'read'));

CREATE POLICY "Allow insert/update/delete access to daily_programs for authorized users"
  ON daily_programs FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('programs', 'write'));

-- Create policies for program_activities table
CREATE POLICY "Allow read access to program_activities for all authenticated users"
  ON program_activities FOR SELECT
  USING (auth.role() = 'authenticated' OR auth.has_feature_access('programs', 'read'));

CREATE POLICY "Allow insert/update/delete access to program_activities for authorized users"
  ON program_activities FOR ALL
  USING (auth.role() = 'authenticated' AND auth.has_feature_access('programs', 'write'));

-- Create policies for access_rights table (only super-admins can manage access rights)
CREATE POLICY "Allow read access to access_rights for super-admins"
  ON access_rights FOR SELECT
  USING (
    (SELECT role FROM users WHERE id = auth.uid()) = 'super-admin'
  );

CREATE POLICY "Allow insert/update/delete access to access_rights for super-admins"
  ON access_rights FOR ALL
  USING (
    (SELECT role FROM users WHERE id = auth.uid()) = 'super-admin'
  );

-- Create policies for magic_links table (only super-admins can manage magic links)
CREATE POLICY "Allow read access to magic_links for super-admins"
  ON magic_links FOR SELECT
  USING (
    (SELECT role FROM users WHERE id = auth.uid()) = 'super-admin'
  );

CREATE POLICY "Allow insert/update/delete access to magic_links for super-admins"
  ON magic_links FOR ALL
  USING (
    (SELECT role FROM users WHERE id = auth.uid()) = 'super-admin'
  );

-- Create policies for access_logs table (only super-admins can view access logs)
CREATE POLICY "Allow read access to access_logs for super-admins"
  ON access_logs FOR SELECT
  USING (
    (SELECT role FROM users WHERE id = auth.uid()) = 'super-admin'
  );

CREATE POLICY "Allow insert access to access_logs for all authenticated users"
  ON access_logs FOR INSERT
  WITH CHECK (auth.role() = 'authenticated');

-- Create policies for users table (users can only see and edit their own data)
CREATE POLICY "Users can view their own user data"
  ON users FOR SELECT
  USING (auth.uid() = id);

CREATE POLICY "Super-admins can view all user data"
  ON users FOR SELECT
  USING ((SELECT role FROM users WHERE id = auth.uid()) = 'super-admin');

CREATE POLICY "Users can update their own user data"
  ON users FOR UPDATE
  USING (auth.uid() = id);

CREATE POLICY "Super-admins can update all user data"
  ON users FOR ALL
  USING ((SELECT role FROM users WHERE id = auth.uid()) = 'super-admin');
