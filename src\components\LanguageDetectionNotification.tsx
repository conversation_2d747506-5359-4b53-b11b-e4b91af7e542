import React, { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '@/contexts/LanguageContext';

const LanguageDetectionNotification = () => {
  const { toast } = useToast();
  const { t } = useTranslation();
  const { language } = useLanguage();
  const [shown, setShown] = useState(false);

  useEffect(() => {
    // Check if we've detected the language and haven't shown the notification yet
    const languageDetected = localStorage.getItem('languageDetected') === 'true';
    const notificationShown = localStorage.getItem('languageNotificationShown') === 'true';
    
    if (languageDetected && !notificationShown && !shown) {
      // Show the notification
      setTimeout(() => {
        toast({
          title: t('common.languageDetected'),
          description: t('common.usingBrowserLanguage', {
            language: language === 'fr' ? t('common.french') : t('common.english')
          }),
          duration: 5000,
        });
        
        // Mark as shown
        localStorage.setItem('languageNotificationShown', 'true');
        setShown(true);
      }, 1500); // Delay to ensure translations are loaded
    }
  }, [toast, t, language, shown]);

  return null; // This component doesn't render anything
};

export default LanguageDetectionNotification;
