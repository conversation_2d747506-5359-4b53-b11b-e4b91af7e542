
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { format } from "date-fns";
import { enUS, fr } from "date-fns/locale";
import { Calendar, PlusCircle, RefreshCw } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const Permissions = () => {
  const { permissions, youths, getYouthById, approvePermission, addPermission, isLoading, refreshData } = useSupabaseData();
  const { user } = useAuth();
  const { t } = useTranslation();
  const { language } = useLanguage();
  const dateLocale = language === 'fr' ? fr : enUS;
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedYouthId, setSelectedYouthId] = useState("");
  const [reason, setReason] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  const handleApprove = (permissionId: string) => {
    if (!user) return;
    approvePermission(permissionId, user.id);
    toast({
      title: t('permissions.permissionApproved'),
      description: t('permissions.permissionApprovedSuccess')
    });
  };

  const handleAddPermission = () => {
    if (!selectedYouthId || !reason || !startDate || !endDate) {
      toast({
        title: t('common.error'),
        description: t('permissions.fillAllFields'),
        variant: "destructive",
      });
      return;
    }

    try {
      addPermission({
        youthId: selectedYouthId,
        reason,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        approved: false,
      });

      setIsDialogOpen(false);
      setSelectedYouthId("");
      setReason("");
      setStartDate("");
      setEndDate("");

      toast({
        title: t('common.success'),
        description: t('permissions.permissionAddSuccess'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('permissions.operationError'),
        variant: "destructive",
      });
    }
  };

  // Format date range
  const formatDateRange = (start: Date, end: Date) => {
    if (format(start, "yyyy-MM-dd") === format(end, "yyyy-MM-dd")) {
      return `${format(start, "dd MMM yyyy", { locale: dateLocale })} (${format(start, "HH:mm")} - ${format(end, "HH:mm")})`;
    }
    return `${format(start, "dd MMM", { locale: dateLocale })} - ${format(end, "dd MMM yyyy", { locale: dateLocale })}`;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('permissions.title')}</h1>
          <p className="text-gray-500">{t('permissions.subtitle')}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.permissions}>
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.permissions ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </Button>
          <Button onClick={() => setIsDialogOpen(true)}>
            <PlusCircle className="h-4 w-4 mr-2" />
            {t('permissions.newPermission')}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{t('permissions.permissionRequests')}</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading.permissions ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.lastName')}</TableHead>
                    <TableHead>{t('common.firstName')}</TableHead>
                    <TableHead>{t('permissions.reason')}</TableHead>
                    <TableHead>{t('permissions.period')}</TableHead>
                    <TableHead>{t('permissions.status')}</TableHead>
                    <TableHead className="w-32">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {permissions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-gray-500">
                        {t('permissions.noPermissions')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    permissions.map((permission) => {
                      const youth = getYouthById(permission.youthId);

                      return (
                        <TableRow key={permission.id}>
                          <TableCell>{youth?.lastName}</TableCell>
                          <TableCell>{youth?.firstName}</TableCell>
                          <TableCell>{permission.reason}</TableCell>
                          <TableCell>
                            <div className="flex items-center">
                              <Calendar className="h-4 w-4 mr-1" />
                              {formatDateRange(permission.startDate, permission.endDate)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {permission.approved ? (
                              <Badge className="bg-green-500">{t('permissions.approved')}</Badge>
                            ) : (
                              <Badge variant="outline" className="border-orange-500 text-orange-500">
                                {t('permissions.pending')}
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            {!permission.approved && (
                              <Button
                                size="sm"
                                onClick={() => handleApprove(permission.id)}
                              >
                                {t('permissions.approve')}
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('permissions.newPermission')}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4 flex-1 overflow-y-auto pr-2">
            <div className="space-y-2">
              <Label htmlFor="youth">{t('youths.title')}</Label>
              <Select
                value={selectedYouthId}
                onValueChange={setSelectedYouthId}
              >
                <SelectTrigger id="youth">
                  <SelectValue placeholder={t('permissions.selectYouth')} />
                </SelectTrigger>
                <SelectContent>
                  {youths.map((youth) => (
                    <SelectItem key={youth.id} value={youth.id}>
                      {youth.firstName} {youth.lastName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="reason">{t('permissions.reason')}</Label>
              <Input
                id="reason"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                placeholder={t('permissions.reasonPlaceholder')}
                autoComplete="off"
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="startDate">{t('common.startDate')}</Label>
                <Input
                  id="startDate"
                  type="datetime-local"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="endDate">{t('common.endDate')}</Label>
                <Input
                  id="endDate"
                  type="datetime-local"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>
            </div>
          </div>
          <DialogFooter className="pt-2 border-t mt-2">
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleAddPermission}>
              {t('common.add')}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Permissions;
