
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { Building, MapPin, PlusCircle, RefreshCw, Users } from "lucide-react";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";

const Districts = () => {
  const {
    districts,
    regions,
    assemblies,
    youths,
    addDistrict,
    getRegionById,
    getAssembliesByDistrict,
    getYouthsByDistrict,
    isLoading,
    refreshData
  } = useSupabaseData();

  const { t } = useTranslation();
  const [newDistrictName, setNewDistrictName] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // State for district details dialog
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedDistrictId, setSelectedDistrictId] = useState<string | null>(null);

  const handleAddDistrict = (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: Record<string, string> = {};

    if (!newDistrictName.trim()) {
      newErrors.name = t('districts.districtNameRequired');
    }

    if (!selectedRegion) {
      newErrors.region = t('districts.regionRequired');
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    // Check if district already exists in the selected region
    if (districts.some(
      district =>
        district.name.toLowerCase() === newDistrictName.trim().toLowerCase() &&
        district.regionId === selectedRegion
    )) {
      setErrors({ name: t('districts.districtExists') });
      return;
    }

    try {
      addDistrict(newDistrictName.trim(), selectedRegion);
      toast({
        title: t('districts.districtAdded'),
        description: t('districts.districtAddedSuccess'),
      });
      setNewDistrictName("");
      setErrors({});
    } catch (error) {
      console.error("Error adding district:", error);
      toast({
        title: t('common.error'),
        description: t('districts.districtAddError'),
        variant: "destructive",
      });
    }
  };

  const handleShowDetails = (districtId: string) => {
    setSelectedDistrictId(districtId);
    setIsDetailsDialogOpen(true);
  };

  // Get the selected district object
  const selectedDistrict = selectedDistrictId
    ? districts.find(district => district.id === selectedDistrictId)
    : null;

  // Get assemblies for the selected district
  const districtAssemblies = selectedDistrictId
    ? getAssembliesByDistrict(selectedDistrictId)
    : [];

  // Get youths for the selected district
  const districtYouths = selectedDistrictId
    ? getYouthsByDistrict(selectedDistrictId)
    : [];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">{t('districts.title')}</h1>
        <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.districts}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.districts ? 'animate-spin' : ''}`} />
          {t('common.refresh')}
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>{t('districts.addDistrict')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAddDistrict} className="space-y-4">
              <div className="space-y-2">
                <Input
                  placeholder={t('districts.districtNamePlaceholder')}
                  value={newDistrictName}
                  onChange={(e) => {
                    setNewDistrictName(e.target.value);
                    if (errors.name) {
                      setErrors(prev => ({ ...prev, name: "" }));
                    }
                  }}
                  className={errors.name ? "border-destructive" : ""}
                />
                {errors.name && <p className="text-sm text-destructive">{errors.name}</p>}
              </div>

              <div className="space-y-2">
                <Select
                  value={selectedRegion}
                  onValueChange={(value) => {
                    setSelectedRegion(value);
                    if (errors.region) {
                      setErrors(prev => ({ ...prev, region: "" }));
                    }
                  }}
                >
                  <SelectTrigger className={errors.region ? "border-destructive" : ""}>
                    <SelectValue placeholder={t('districts.selectRegion')} />
                  </SelectTrigger>
                  <SelectContent>
                    {regions.map((region) => (
                      <SelectItem key={region.id} value={region.id}>
                        {region.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.region && <p className="text-sm text-destructive">{errors.region}</p>}
              </div>

              <Button type="submit" className="w-full">
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('common.add')}
              </Button>
            </form>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>{t('districts.districtList')}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('common.name')}</TableHead>
                    <TableHead>{t('districts.region')}</TableHead>
                    <TableHead className="w-24">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {districts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4 text-gray-500">
                        {t('districts.noDistricts')}
                      </TableCell>
                    </TableRow>
                  ) : (
                    districts.map((district) => (
                      <TableRow key={district.id}>
                        <TableCell>{district.name}</TableCell>
                        <TableCell>{getRegionById(district.regionId)?.name}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleShowDetails(district.id)}
                          >
                            {t('districts.details')}
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* District Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('districts.districtDetails')}</DialogTitle>
          </DialogHeader>

          {selectedDistrict && (
            <div className="space-y-6 py-4 overflow-y-auto">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">{selectedDistrict.name}</h3>
                </div>
                <p className="text-sm text-gray-500">
                  {t('districts.region')}: {getRegionById(selectedDistrict.regionId)?.name}
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-primary" />
                  <h3 className="text-md font-medium">{t('assemblies.title')}</h3>
                </div>
                {districtAssemblies.length === 0 ? (
                  <p className="text-sm text-gray-500">{t('assemblies.noAssemblies')}</p>
                ) : (
                  <ul className="space-y-1 pl-7 list-disc text-sm">
                    {districtAssemblies.map(assembly => (
                      <li key={assembly.id}>{assembly.name}</li>
                    ))}
                  </ul>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-primary" />
                  <h3 className="text-md font-medium">{t('youths.title')}</h3>
                </div>
                <p className="text-sm">
                  {t('common.total')}: <span className="font-medium">{districtYouths.length}</span>
                </p>
                <div className="flex gap-4 text-sm">
                  <p>
                    {t('common.male')}: <span className="font-medium">
                      {districtYouths.filter(youth => youth.gender === 'male').length}
                    </span>
                  </p>
                  <p>
                    {t('common.female')}: <span className="font-medium">
                      {districtYouths.filter(youth => youth.gender === 'female').length}
                    </span>
                  </p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Districts;
