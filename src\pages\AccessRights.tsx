import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useSupabaseData } from "@/contexts/SupabaseDataContext";
import { toast } from "@/hooks/use-toast";
import { AccessType, FeatureType } from "@/types";
import { format } from "date-fns";
import { enUS, fr } from "date-fns/locale";
import { Copy, Link, PlusCircle, RefreshCw, Trash } from "lucide-react";
import { useState } from "react";
import { useTranslation } from "react-i18next";

const AccessRights = () => {
  const { user } = useAuth();
  const { t } = useTranslation();
  const { language } = useLanguage();
  const dateLocale = language === 'fr' ? fr : enUS;

  const featureLabels: Record<FeatureType, string> = {
    youths: t('accessRights.featureLabels.youths'),
    payments: t('accessRights.featureLabels.payments'),
    permissions: t('accessRights.featureLabels.permissions'),
    programs: t('accessRights.featureLabels.programs'),
    sermons: t('accessRights.featureLabels.sermons'),
    regions: t('accessRights.featureLabels.regions'),
    districts: t('accessRights.featureLabels.districts'),
    assemblies: t('accessRights.featureLabels.assemblies')
  };

  const accessTypeLabels: Record<AccessType, string> = {
    read: t('accessRights.read'),
    write: t('accessRights.write'),
    'read-write': t('accessRights.readWrite')
  };

  const {
    accessRights,
    magicLinks,
    accessLogs,
    createAccessRight,
    updateAccessRight,
    deleteAccessRight,
    createMagicLink,
    revokeMagicLink,
    deleteMagicLink,
    getMagicLinksByAccessRightId,
    getAccessRightById,
    getAccessLogsByMagicLinkId,
    isLoading,
    refreshData
  } = useSupabaseData();

  const [isCreateRightDialogOpen, setIsCreateRightDialogOpen] = useState(false);
  const [isCreateLinkDialogOpen, setIsCreateLinkDialogOpen] = useState(false);
  const [selectedAccessRightId, setSelectedAccessRightId] = useState<string | null>(null);
  const [selectedMagicLinkId, setSelectedMagicLinkId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("rights");

  // États pour le formulaire de création de droit d'accès
  const [rightName, setRightName] = useState("");
  const [rightDescription, setRightDescription] = useState("");
  const [selectedFeatures, setSelectedFeatures] = useState<{
    feature: FeatureType;
    access: AccessType;
  }[]>([]);

  // États pour le formulaire de création de lien magique
  const [linkName, setLinkName] = useState("");
  const [linkExpiration, setLinkExpiration] = useState("");

  const handleAddFeature = (feature: FeatureType, access: AccessType) => {
    // Vérifier si la fonctionnalité est déjà sélectionnée
    const existingIndex = selectedFeatures.findIndex(f => f.feature === feature);

    if (existingIndex >= 0) {
      // Mettre à jour l'accès si la fonctionnalité existe déjà
      const updatedFeatures = [...selectedFeatures];
      updatedFeatures[existingIndex] = { feature, access };
      setSelectedFeatures(updatedFeatures);
    } else {
      // Ajouter la nouvelle fonctionnalité
      setSelectedFeatures([...selectedFeatures, { feature, access }]);
    }
  };

  const handleRemoveFeature = (feature: FeatureType) => {
    setSelectedFeatures(selectedFeatures.filter(f => f.feature !== feature));
  };

  const handleCreateAccessRight = () => {
    if (!rightName || selectedFeatures.length === 0) {
      toast({
        title: t('common.error'),
        description: t('accessRights.fillAllFields'),
        variant: "destructive",
      });
      return;
    }

    if (!user) {
      toast({
        title: t('common.error'),
        description: t('accessRights.loginRequired'),
        variant: "destructive",
      });
      return;
    }

    try {
      createAccessRight({
        name: rightName,
        description: rightDescription,
        features: selectedFeatures,
        createdBy: user.id
      });

      setIsCreateRightDialogOpen(false);
      setRightName("");
      setRightDescription("");
      setSelectedFeatures([]);

      toast({
        title: t('common.success'),
        description: t('accessRights.rightCreated'),
        variant: "success",
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('accessRights.rightCreateError'),
        variant: "destructive",
      });
    }
  };

  const handleCreateMagicLink = () => {
    if (!selectedAccessRightId || !linkName) {
      toast({
        title: t('common.error'),
        description: t('accessRights.fillAllFields'),
        variant: "destructive",
      });
      return;
    }

    try {
      const expiresAt = linkExpiration ? new Date(linkExpiration) : undefined;
      createMagicLink(selectedAccessRightId, linkName, expiresAt);

      setIsCreateLinkDialogOpen(false);
      setLinkName("");
      setLinkExpiration("");

      toast({
        title: t('common.success'),
        description: t('accessRights.linkCreated'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('accessRights.linkCreateError'),
        variant: "destructive",
      });
    }
  };

  const handleDeleteAccessRight = (id: string) => {
    try {
      deleteAccessRight(id);
      toast({
        title: t('common.success'),
        description: t('accessRights.rightDeleted'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('accessRights.rightDeleteError'),
        variant: "destructive",
      });
    }
  };

  const handleRevokeMagicLink = (id: string) => {
    try {
      revokeMagicLink(id);
      toast({
        title: t('common.success'),
        description: t('accessRights.linkRevoked'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('accessRights.linkRevokeError'),
        variant: "destructive",
      });
    }
  };

  const handleDeleteMagicLink = (id: string) => {
    try {
      deleteMagicLink(id);
      toast({
        title: t('common.success'),
        description: t('accessRights.linkDeleted'),
      });
    } catch (error) {
      toast({
        title: t('common.error'),
        description: t('accessRights.linkDeleteError'),
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = (text: string) => {
    const fullUrl = window.location.origin + text;

    // Créer un élément input temporaire
    const tempInput = document.createElement('input');
    tempInput.value = fullUrl;
    document.body.appendChild(tempInput);

    // Sélectionner et copier le texte
    tempInput.select();
    tempInput.setSelectionRange(0, 99999); // Pour les appareils mobiles

    try {
      // Exécuter la commande de copie
      const successful = document.execCommand('copy');

      if (successful) {
        toast({
          title: t('common.copied'),
          description: t('accessRights.linkCopied'),
        });
      } else {
        // Utiliser l'API Clipboard si execCommand échoue
        navigator.clipboard.writeText(fullUrl).then(
          () => {
            toast({
              title: t('common.copied'),
              description: t('accessRights.linkCopied'),
            });
          },
          (err) => {
            toast({
              title: t('common.error'),
              description: t('accessRights.linkCopyError'),
              variant: "destructive",
            });
            console.error('Error copying link:', err);
          }
        );
      }
    } catch (err) {
      toast({
        title: t('common.error'),
        description: t('accessRights.linkCopyError'),
        variant: "destructive",
      });
      console.error('Error copying link:', err);
    } finally {
      // Nettoyer l'élément temporaire
      document.body.removeChild(tempInput);
    }
  };

  const getFullLink = (url: string) => {
    return `${window.location.origin}${url}`;
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold mb-1">{t('accessRights.title')}</h1>
          <p className="text-gray-500">{t('accessRights.subtitle')}</p>
        </div>
        <Button variant="outline" size="sm" onClick={() => refreshData()} disabled={isLoading.accessRights}>
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading.accessRights ? 'animate-spin' : ''}`} />
          {t('common.refresh')}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="rights">{t('accessRights.accessRights')}</TabsTrigger>
          <TabsTrigger value="links">{t('accessRights.magicLinks')}</TabsTrigger>
          <TabsTrigger value="logs">{t('accessRights.accessLogs')}</TabsTrigger>
        </TabsList>

        <TabsContent value="rights" className="space-y-4">
          <div className="flex justify-end mb-4">
            <Button onClick={() => setIsCreateRightDialogOpen(true)}>
              <PlusCircle className="h-4 w-4 mr-2" />
              {t('accessRights.newAccessRight')}
            </Button>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>{t('accessRights.accessRights')}</CardTitle>
              <CardDescription>{t('accessRights.accessRightsList')}</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading.accessRights ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('common.name')}</TableHead>
                        <TableHead>{t('common.description')}</TableHead>
                        <TableHead>{t('accessRights.features')}</TableHead>
                        <TableHead>{t('accessRights.creationDate')}</TableHead>
                        <TableHead>{t('accessRights.activeLinks')}</TableHead>
                        <TableHead className="w-32">{t('common.actions')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {accessRights.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-4 text-gray-500">
                            {t('accessRights.noAccessRights')}
                          </TableCell>
                        </TableRow>
                      ) : (
                        accessRights.map((right) => {
                          const activeLinks = getMagicLinksByAccessRightId(right.id).filter(link => link.active);

                          return (
                            <TableRow key={right.id}>
                              <TableCell className="font-medium">{right.name}</TableCell>
                              <TableCell>{right.description || "-"}</TableCell>
                              <TableCell>
                                <div className="flex flex-wrap gap-1">
                                  {right.features.map((feature) => (
                                    <Badge key={feature.feature} variant="outline" className="text-xs">
                                      {featureLabels[feature.feature]}: {accessTypeLabels[feature.access]}
                                    </Badge>
                                  ))}
                                </div>
                              </TableCell>
                              <TableCell>
                                {format(new Date(right.createdAt), 'dd/MM/yyyy', { locale: dateLocale })}
                              </TableCell>
                              <TableCell>{activeLinks.length}</TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                      setSelectedAccessRightId(right.id);
                                      setIsCreateLinkDialogOpen(true);
                                    }}
                                  >
                                    <Link className="h-4 w-4 mr-1" />
                                    {t('accessRights.link')}
                                  </Button>
                                  <Button
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => handleDeleteAccessRight(right.id)}
                                  >
                                    <Trash className="h-4 w-4" />
                                  </Button>
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="links" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('accessRights.magicLinks')}</CardTitle>
              <CardDescription>{t('accessRights.magicLinksList')}</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading.magicLinks ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('common.name')}</TableHead>
                        <TableHead>{t('accessRights.accessRight')}</TableHead>
                        <TableHead>{t('accessRights.creationDate')}</TableHead>
                        <TableHead>{t('accessRights.expirationDate')}</TableHead>
                        <TableHead>{t('accessRights.views')}</TableHead>
                        <TableHead>{t('accessRights.edits')}</TableHead>
                        <TableHead>{t('common.status')}</TableHead>
                        <TableHead className="w-32">{t('common.actions')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {magicLinks.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-4 text-gray-500">
                            {t('accessRights.noMagicLinks')}
                          </TableCell>
                        </TableRow>
                      ) : (
                        magicLinks.map((link) => {
                          const accessRight = getAccessRightById(link.accessRightId);

                          return (
                            <TableRow key={link.id}>
                              <TableCell className="font-medium">{link.name}</TableCell>
                              <TableCell>{accessRight?.name || "-"}</TableCell>
                              <TableCell>
                                {format(new Date(link.createdAt), 'dd/MM/yyyy', { locale: dateLocale })}
                              </TableCell>
                              <TableCell>
                                {link.expiresAt
                                  ? format(new Date(link.expiresAt), 'dd/MM/yyyy', { locale: dateLocale })
                                  : t('accessRights.noExpiration')}
                              </TableCell>
                              <TableCell>{link.viewCount}</TableCell>
                              <TableCell>{link.editCount}</TableCell>
                              <TableCell>
                                {link.active ? (
                                  <Badge className="bg-green-500">{t('common.active')}</Badge>
                                ) : (
                                  <Badge variant="outline" className="border-red-500 text-red-500">
                                    {t('accessRights.revoked')}
                                  </Badge>
                                )}
                              </TableCell>
                              <TableCell>
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => copyToClipboard(link.url)}
                                  >
                                    <Copy className="h-4 w-4" />
                                  </Button>
                                  {link.active ? (
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      onClick={() => handleRevokeMagicLink(link.id)}
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  ) : (
                                    <Button
                                      variant="destructive"
                                      size="sm"
                                      onClick={() => handleDeleteMagicLink(link.id)}
                                    >
                                      <Trash className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                              </TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>{t('accessRights.accessLogs')}</CardTitle>
              <CardDescription>{t('accessRights.accessLogsDescription')}</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading.accessLogs ? (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>{t('accessRights.date')}</TableHead>
                        <TableHead>{t('accessRights.link')}</TableHead>
                        <TableHead>{t('accessRights.action')}</TableHead>
                        <TableHead>{t('accessRights.feature')}</TableHead>
                        <TableHead>{t('accessRights.details')}</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {accessLogs.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-4 text-gray-500">
                            {t('accessRights.noAccessLogs')}
                          </TableCell>
                        </TableRow>
                      ) : (
                        accessLogs.map((log) => {
                          const magicLink = magicLinks.find(link => link.id === log.magicLinkId);

                          return (
                            <TableRow key={log.id}>
                              <TableCell>
                                {format(new Date(log.timestamp), 'dd/MM/yyyy HH:mm', { locale: dateLocale })}
                              </TableCell>
                              <TableCell>{magicLink?.name || log.magicLinkId}</TableCell>
                              <TableCell>
                                <Badge variant={log.action === 'view' ? 'outline' : 'default'}>
                                  {log.action === 'view' ? t('accessRights.read') : t('accessRights.write')}
                                </Badge>
                              </TableCell>
                              <TableCell>{featureLabels[log.feature]}</TableCell>
                              <TableCell>{log.details || "-"}</TableCell>
                            </TableRow>
                          );
                        })
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Access rights creation dialog */}
      <Dialog open={isCreateRightDialogOpen} onOpenChange={setIsCreateRightDialogOpen}>
        <DialogContent className="max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('accessRights.newAccessRight')}</DialogTitle>
            <DialogDescription>
              {t('accessRights.defineAccessRights')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4 flex-1 overflow-y-auto pr-2">
            <div className="space-y-2">
              <Label htmlFor="right-name">{t('common.name')}</Label>
              <Input
                id="right-name"
                value={rightName}
                onChange={(e) => setRightName(e.target.value)}
                placeholder={t('accessRights.accessRightNamePlaceholder')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="right-description">{t('common.description')} ({t('common.optional')})</Label>
              <Textarea
                id="right-description"
                value={rightDescription}
                onChange={(e) => setRightDescription(e.target.value)}
                placeholder={t('accessRights.accessRightDescriptionPlaceholder')}
              />
            </div>
            <div className="space-y-2">
              <Label>{t('accessRights.features')}</Label>
              <div className="border rounded-md p-4 space-y-4">
                {Object.entries(featureLabels).map(([feature, label]) => (
                  <div key={feature} className="flex items-center justify-between">
                    <div className="font-medium">{label}</div>
                    <div className="flex items-center space-x-2">
                      <Select
                        value={selectedFeatures.find(f => f.feature === feature as FeatureType)?.access || "none"}
                        onValueChange={(value) => {
                          if (value && value !== "none") {
                            handleAddFeature(feature as FeatureType, value as AccessType);
                          } else {
                            handleRemoveFeature(feature as FeatureType);
                          }
                        }}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder={t('accessRights.access')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">{t('accessRights.none')}</SelectItem>
                          <SelectItem value="read">{t('accessRights.read')}</SelectItem>
                          <SelectItem value="write">{t('accessRights.write')}</SelectItem>
                          <SelectItem value="read-write">{t('accessRights.readWrite')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateRightDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleCreateAccessRight}>{t('common.create')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Magic link creation dialog */}
      <Dialog open={isCreateLinkDialogOpen} onOpenChange={setIsCreateLinkDialogOpen}>
        <DialogContent className="max-h-[90vh] flex flex-col" onInteractOutside={(e) => e.preventDefault()}>
          <DialogHeader>
            <DialogTitle>{t('accessRights.newMagicLink')}</DialogTitle>
            <DialogDescription>
              {t('accessRights.createLinkDescription')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4 flex-1 overflow-y-auto pr-2">
            <div className="space-y-2">
              <Label htmlFor="link-name">{t('common.name')}</Label>
              <Input
                id="link-name"
                value={linkName}
                onChange={(e) => setLinkName(e.target.value)}
                placeholder={t('accessRights.magicLinkNamePlaceholder')}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="access-right">{t('accessRights.accessRight')}</Label>
              <Select
                value={selectedAccessRightId || ""}
                onValueChange={setSelectedAccessRightId}
              >
                <SelectTrigger id="access-right">
                  <SelectValue placeholder={t('accessRights.selectAccessRight')} />
                </SelectTrigger>
                <SelectContent>
                  {accessRights.map((right) => (
                    <SelectItem key={right.id} value={right.id}>
                      {right.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="link-expiration">{t('accessRights.expirationDate')} ({t('common.optional')})</Label>
              <Input
                id="link-expiration"
                type="date"
                value={linkExpiration}
                onChange={(e) => setLinkExpiration(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateLinkDialogOpen(false)}>
              {t('common.cancel')}
            </Button>
            <Button onClick={handleCreateMagicLink}>{t('common.create')}</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AccessRights;
