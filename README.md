# Camp Avenir Gestion

A comprehensive youth camp management application built with React, TypeScript, and Supabase. This application provides a complete solution for managing youth camps, including participant registration, program planning, permissions management, and real-time data synchronization.

![Dashboard Screenshot](./assets/dash.jpg)

## 🎯 Project Description

Camp Avenir Gestion is a modern web application designed to streamline the management of youth camps. It offers an intuitive interface for camp organizers to handle all aspects of camp administration, from participant registration to daily program planning and access control management.

## ✨ Key Features

### 👥 Youth Management
- **Registration System**: Comprehensive youth registration with personal information
- **Assembly Organization**: Hierarchical organization by regions, districts, and assemblies
- **Real-time Updates**: Live synchronization of participant data across all devices
- **Advanced Search & Filtering**: Find participants by various criteria
- **Bulk Operations**: Efficient management of multiple participants

### 🏛️ Organizational Structure
- **Regions Management**: Top-level geographical organization
- **Districts Management**: Mid-level administrative divisions
- **Assemblies Management**: Local congregation-level organization
- **Hierarchical Data**: Automatic relationship management between organizational levels

### 🔗 Magic Link Access System
- **Secure Access**: Generate time-limited access links for external collaborators
- **Granular Permissions**: Define specific feature access for each magic link
- **Usage Tracking**: Monitor link usage with detailed access logs
- **Expiration Control**: Set custom expiration dates for enhanced security

### 🌍 Multi-language Support
- **Bilingual Interface**: Full support for French and English
- **Dynamic Language Switching**: Real-time language changes without page reload
- **Browser Detection**: Automatic language detection based on browser settings
- **Comprehensive Translation**: All UI elements, messages, and content translated

### 📊 Real-time Data Synchronization
- **Supabase Integration**: Real-time database updates across all connected clients
- **Offline Support**: Graceful handling of network connectivity issues
- **Data Consistency**: Automatic conflict resolution and data synchronization
- **Performance Optimization**: Efficient data loading and caching strategies

### 📋 Program Management
- **Daily Programs**: Create and manage daily camp schedules
- **Activity Planning**: Add, modify, and organize camp activities
- **Time Management**: Set precise start and end times for activities
- **Program Sharing**: Generate shareable links for programs with real-time updates
- **Export Options**: Download programs in Word format for offline use
- **Print Support**: Professional printing capabilities for program distribution

### 🔐 Access Rights & Permissions
- **Role-based Access**: Super-admin, admin, moderator, and viewer roles
- **Feature-level Permissions**: Granular control over application features
- **Permission Requests**: Youth permission system for absences and special requests
- **Approval Workflow**: Streamlined approval process for permission requests
- **Audit Trail**: Complete logging of all permission-related activities

### 📖 Sermon Management
- **Sermon Library**: Comprehensive collection of sermons with metadata
- **Content Management**: Rich text editing for sermon content
- **Search Functionality**: Find sermons by preacher, date, or theme
- **Print Support**: Professional formatting for sermon distribution
- **Categorization**: Organize sermons by themes and biblical passages

### 💰 Financial Tracking
- **Payment Management**: Track participant payments and contributions
- **Multiple Payment Methods**: Support for cash, mobile money, bank transfers
- **Payment History**: Complete transaction history for each participant
- **Financial Reports**: Generate payment summaries and financial overviews

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern React with hooks and functional components
- **TypeScript** - Type-safe development with enhanced IDE support
- **Vite** - Fast build tool and development server
- **Tailwind CSS** - Utility-first CSS framework for rapid styling
- **shadcn/ui** - High-quality, accessible UI components
- **React Router** - Client-side routing for single-page application

### Backend & Database
- **Supabase** - Backend-as-a-Service with PostgreSQL database
- **Row Level Security (RLS)** - Database-level security policies
- **Real-time Subscriptions** - Live data updates across clients
- **Authentication** - Secure user authentication and session management

### State Management & Data Fetching
- **TanStack Query** - Powerful data synchronization and caching
- **React Context** - Global state management for user sessions
- **Custom Hooks** - Reusable logic for data operations

### Internationalization
- **i18next** - Comprehensive internationalization framework
- **react-i18next** - React integration for i18next
- **Language Detection** - Automatic browser language detection

### UI/UX Libraries
- **Lucide React** - Beautiful, customizable icons
- **React Hook Form** - Performant forms with easy validation
- **React Beautiful DnD** - Drag and drop functionality
- **Recharts** - Responsive chart library for data visualization
- **React to Print** - Client-side printing capabilities

### Development Tools
- **ESLint** - Code linting and quality assurance
- **TypeScript ESLint** - TypeScript-specific linting rules
- **Autoprefixer** - Automatic CSS vendor prefixing
- **PostCSS** - CSS processing and optimization

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ and npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- Supabase account and project setup

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/Paolo068/camp-avenir-gestion.git
   cd camp-avenir-gestion
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**

   Copy the example environment file and configure your Supabase credentials:
   ```bash
   cp .env.example .env.local
   ```

   Edit `.env.local` and add your Supabase configuration:
   ```env
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Start the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**

   Navigate to `http://localhost:8080` to view the application.

## 🗄️ Database Setup

### Supabase Configuration

1. **Create a new Supabase project** at [supabase.com](https://supabase.com)

2. **Run the database schema**

   Execute the SQL commands in `database-schema.sql` in your Supabase SQL editor to create all necessary tables.

3. **Set up Row Level Security (RLS)**

   Apply the security policies from `rls-policies.sql` to ensure proper data access control.

4. **Configure Authentication**

   Set up authentication providers in your Supabase dashboard as needed.

### Required Tables

The application uses the following main tables:
- `regions` - Geographical regions
- `districts` - Administrative districts
- `assemblies` - Local assemblies/congregations
- `youths` - Participant information
- `payments` - Financial transactions
- `permissions` - Permission requests and approvals
- `sermons` - Sermon library
- `daily_programs` - Program schedules
- `program_activities` - Individual activities
- `registration_links` - Registration link management
- `access_rights` - Permission templates
- `magic_links` - Secure access links
- `access_logs` - Usage tracking
- `users` - User accounts and roles

## 📸 Screenshots

### Main Dashboard
![Dashboard](./assets/dash.jpg)
*The main dashboard provides an overview of camp statistics, recent activities, and quick access to key features.*

## 🚀 Deployment

### Netlify Deployment (Recommended)

This project is configured for easy deployment on Netlify:

1. **Connect your repository** to Netlify
2. **Set environment variables** in Netlify dashboard:
   - `VITE_SUPABASE_URL`
   - `VITE_SUPABASE_ANON_KEY`
3. **Deploy** - Netlify will automatically build and deploy your application

The `netlify.toml` file is already configured with the correct build settings.

### Manual Deployment

1. **Build the project**
   ```bash
   npm run build
   ```

2. **Deploy the `dist` folder** to your hosting provider

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in this repository
- Contact the development team
- Check the documentation in the `docs/` folder

## 🔗 Links

- **Live Demo**: [Camp Avenir Gestion](https://camp-avenir-gestion.netlify.app)
- **Supabase Project**: Configure your own Supabase instance
- **Documentation**: See `features-summary.md` and `resume-fonctionnalites.md` for detailed feature descriptions
